const { TronWeb } = require('tronweb');

// Address verification script
console.log('🔍 Verifying USDT Contract Addresses...\n');

// Expected addresses
const EXPECTED_BASE58 = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
const EXPECTED_HEX = '0x41eca9bc828a3005b9a3b909f2cc5c2a54794de05f';
const TARGET_WALLET = 'TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW';

// Initialize TronWeb for address conversion
const tronWeb = new TronWeb({
    fullHost: 'https://nile.trongrid.io'
});

function verifyAddressConversion() {
    console.log('📋 Address Verification:');
    console.log('========================');
    
    try {
        // Convert Base58 to Hex
        const convertedHex = tronWeb.address.toHex(EXPECTED_BASE58);
        const convertedHexWith0x = '0x' + convertedHex;
        console.log(`✅ Base58: ${EXPECTED_BASE58}`);
        console.log(`✅ Hex:    ${convertedHex}`);
        console.log(`✅ With 0x: ${convertedHexWith0x}`);
        console.log(`✅ Expected: ${EXPECTED_HEX}`);

        if (convertedHexWith0x.toLowerCase() === EXPECTED_HEX.toLowerCase()) {
            console.log('🎯 ✅ Address conversion is CORRECT!\n');
        } else {
            console.log('❌ Address conversion MISMATCH!\n');
            console.log(`   Converted: ${convertedHexWith0x.toLowerCase()}`);
            console.log(`   Expected:  ${EXPECTED_HEX.toLowerCase()}\n`);
            return false;
        }
        
        // Verify target wallet
        const targetHex = tronWeb.address.toHex(TARGET_WALLET);
        console.log(`🎯 Target Wallet:`);
        console.log(`   Base58: ${TARGET_WALLET}`);
        console.log(`   Hex:    ${targetHex}\n`);
        
        return true;
        
    } catch (error) {
        console.error('❌ Address verification failed:', error);
        return false;
    }
}

function checkContractAddresses() {
    console.log('📂 Checking Contract Files:');
    console.log('============================');
    
    const fs = require('fs');
    const path = require('path');
    
    // Check USDTMimicFlash.sol
    try {
        const mimicPath = path.join(__dirname, 'contracts', 'USDTMimicFlash.sol');
        const mimicContent = fs.readFileSync(mimicPath, 'utf8');
        
        if (mimicContent.includes(EXPECTED_HEX)) {
            console.log('✅ USDTMimicFlash.sol - Address is correct');
        } else if (mimicContent.includes(EXPECTED_BASE58)) {
            console.log('❌ USDTMimicFlash.sol - Using Base58 instead of Hex');
        } else {
            console.log('⚠️  USDTMimicFlash.sol - Address not found or different');
        }
    } catch (error) {
        console.log('❌ USDTMimicFlash.sol - File not found');
    }
    
    // Check USDTImpersonator.sol
    try {
        const impersonatorPath = path.join(__dirname, 'contracts', 'USDTImpersonator.sol');
        const impersonatorContent = fs.readFileSync(impersonatorPath, 'utf8');
        
        if (impersonatorContent.includes(EXPECTED_HEX)) {
            console.log('✅ USDTImpersonator.sol - Address is correct');
        } else if (impersonatorContent.includes(EXPECTED_BASE58)) {
            console.log('❌ USDTImpersonator.sol - Using Base58 instead of Hex');
        } else {
            console.log('⚠️  USDTImpersonator.sol - Address not found or different');
        }
    } catch (error) {
        console.log('❌ USDTImpersonator.sol - File not found');
    }
    
    // Check USDTFlashV2.sol
    try {
        const flashV2Path = path.join(__dirname, 'contracts', 'USDTFlashV2.sol');
        const flashV2Content = fs.readFileSync(flashV2Path, 'utf8');
        
        if (flashV2Content.includes('setRealUSDTContract')) {
            console.log('✅ USDTFlashV2.sol - Uses dynamic address setting');
        } else {
            console.log('⚠️  USDTFlashV2.sol - Check address configuration');
        }
    } catch (error) {
        console.log('❌ USDTFlashV2.sol - File not found');
    }
    
    console.log('');
}

function checkDeployedContracts() {
    console.log('🚀 Checking Deployed Contracts:');
    console.log('================================');
    
    const fs = require('fs');
    const path = require('path');
    
    const deployedDir = path.join(__dirname, 'deployed');
    
    if (!fs.existsSync(deployedDir)) {
        console.log('📁 No deployed contracts found');
        return;
    }
    
    const files = fs.readdirSync(deployedDir);
    
    files.forEach(file => {
        if (file.endsWith('.json')) {
            try {
                const contractData = JSON.parse(fs.readFileSync(path.join(deployedDir, file), 'utf8'));
                console.log(`✅ ${file}: ${contractData.address}`);
            } catch (error) {
                console.log(`❌ ${file}: Error reading file`);
            }
        }
    });
    
    console.log('');
}

async function testCurrentContract() {
    console.log('🧪 Testing Current FlashV2 Contract:');
    console.log('=====================================');
    
    try {
        // Load current contract
        const fs = require('fs');
        const path = require('path');
        
        const flashV2Path = path.join(__dirname, 'deployed', 'USDTFlashV2.json');
        
        if (!fs.existsSync(flashV2Path)) {
            console.log('❌ USDTFlashV2 not deployed');
            return;
        }
        
        const contractData = JSON.parse(fs.readFileSync(flashV2Path, 'utf8'));
        console.log(`📍 Contract Address: ${contractData.address}`);
        
        // Initialize TronWeb with a dummy private key for read-only operations
        const tronWeb = new TronWeb({
            fullHost: 'https://nile.trongrid.io',
            privateKey: '01'.repeat(32) // Dummy key for read operations
        });
        
        // Get contract instance
        const contract = await tronWeb.contract(contractData.abi, contractData.address);
        
        // Check USDT contract address
        try {
            const usdtAddress = await contract.REAL_USDT_CONTRACT().call();
            console.log(`💰 USDT Contract: ${tronWeb.address.fromHex(usdtAddress)}`);
            
            if (tronWeb.address.fromHex(usdtAddress) === EXPECTED_BASE58) {
                console.log('✅ USDT address is correctly set!');
            } else {
                console.log('❌ USDT address mismatch!');
            }
        } catch (error) {
            console.log('⚠️  Could not read USDT contract address');
        }
        
        // Check owner
        try {
            const owner = await contract.owner().call();
            console.log(`👤 Owner: ${tronWeb.address.fromHex(owner)}`);
        } catch (error) {
            console.log('⚠️  Could not read owner');
        }
        
    } catch (error) {
        console.error('❌ Contract test failed:', error);
    }
    
    console.log('');
}

async function main() {
    console.log('🔍 USDT Flash Contract Verification\n');
    console.log('====================================\n');
    
    // Step 1: Verify address conversion
    const addressOK = verifyAddressConversion();
    
    // Step 2: Check contract files
    checkContractAddresses();
    
    // Step 3: Check deployed contracts
    checkDeployedContracts();
    
    // Step 4: Test current contract
    await testCurrentContract();
    
    // Summary
    console.log('📋 Verification Summary:');
    console.log('========================');
    console.log(`✅ USDT Contract (Base58): ${EXPECTED_BASE58}`);
    console.log(`✅ USDT Contract (Hex):    ${EXPECTED_HEX}`);
    console.log(`✅ Target Wallet:          ${TARGET_WALLET}`);
    
    if (addressOK) {
        console.log('\n🎯 All addresses are correctly formatted!');
        console.log('\n📝 Ready for deployment and testing.');
    } else {
        console.log('\n❌ Address verification failed!');
        console.log('\n🔧 Please fix address formats before deployment.');
    }
}

// Run verification
main().catch(console.error);
