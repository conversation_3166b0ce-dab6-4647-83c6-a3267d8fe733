// source: core/contract/account_contract.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global =
    (typeof globalThis !== 'undefined' && globalThis) ||
    (typeof window !== 'undefined' && window) ||
    (typeof global !== 'undefined' && global) ||
    (typeof self !== 'undefined' && self) ||
    function () {
        return this;
    }.call(null) ||
    Function('return this')();

var core_Tron_pb = require('../../core/Tron_pb.cjs');
goog.object.extend(proto, core_Tron_pb);
goog.exportSymbol('TronWebProto.AccountCreateContract', null, global);
goog.exportSymbol('TronWebProto.AccountPermissionUpdateContract', null, global);
goog.exportSymbol('TronWebProto.AccountUpdateContract', null, global);
goog.exportSymbol('TronWebProto.SetAccountIdContract', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
TronWebProto.AccountCreateContract = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(TronWebProto.AccountCreateContract, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    TronWebProto.AccountCreateContract.displayName = 'TronWebProto.AccountCreateContract';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
TronWebProto.AccountUpdateContract = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(TronWebProto.AccountUpdateContract, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    TronWebProto.AccountUpdateContract.displayName = 'TronWebProto.AccountUpdateContract';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
TronWebProto.SetAccountIdContract = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(TronWebProto.SetAccountIdContract, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    TronWebProto.SetAccountIdContract.displayName = 'TronWebProto.SetAccountIdContract';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
TronWebProto.AccountPermissionUpdateContract = function (opt_data) {
    jspb.Message.initialize(this, opt_data, 0, -1, TronWebProto.AccountPermissionUpdateContract.repeatedFields_, null);
};
goog.inherits(TronWebProto.AccountPermissionUpdateContract, jspb.Message);
if (goog.DEBUG && !COMPILED) {
    /**
     * @public
     * @override
     */
    TronWebProto.AccountPermissionUpdateContract.displayName = 'TronWebProto.AccountPermissionUpdateContract';
}

if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    TronWebProto.AccountCreateContract.prototype.toObject = function (opt_includeInstance) {
        return TronWebProto.AccountCreateContract.toObject(opt_includeInstance, this);
    };

    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!TronWebProto.AccountCreateContract} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    TronWebProto.AccountCreateContract.toObject = function (includeInstance, msg) {
        var f,
            obj = {
                ownerAddress: msg.getOwnerAddress_asB64(),
                accountAddress: msg.getAccountAddress_asB64(),
                type: jspb.Message.getFieldWithDefault(msg, 3, 0),
            };

        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!TronWebProto.AccountCreateContract}
 */
TronWebProto.AccountCreateContract.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new TronWebProto.AccountCreateContract();
    return TronWebProto.AccountCreateContract.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!TronWebProto.AccountCreateContract} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!TronWebProto.AccountCreateContract}
 */
TronWebProto.AccountCreateContract.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setOwnerAddress(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setAccountAddress(value);
                break;
            case 3:
                var value = /** @type {!TronWebProto.AccountType} */ (reader.readEnum());
                msg.setType(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
TronWebProto.AccountCreateContract.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    TronWebProto.AccountCreateContract.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!TronWebProto.AccountCreateContract} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
TronWebProto.AccountCreateContract.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getOwnerAddress_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getAccountAddress_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
    f = message.getType();
    if (f !== 0.0) {
        writer.writeEnum(3, f);
    }
};

/**
 * optional bytes owner_address = 1;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.AccountCreateContract.prototype.getOwnerAddress = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * optional bytes owner_address = 1;
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {string}
 */
TronWebProto.AccountCreateContract.prototype.getOwnerAddress_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getOwnerAddress()));
};

/**
 * optional bytes owner_address = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {!Uint8Array}
 */
TronWebProto.AccountCreateContract.prototype.getOwnerAddress_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getOwnerAddress()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.AccountCreateContract} returns this
 */
TronWebProto.AccountCreateContract.prototype.setOwnerAddress = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};

/**
 * optional bytes account_address = 2;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.AccountCreateContract.prototype.getAccountAddress = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * optional bytes account_address = 2;
 * This is a type-conversion wrapper around `getAccountAddress()`
 * @return {string}
 */
TronWebProto.AccountCreateContract.prototype.getAccountAddress_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getAccountAddress()));
};

/**
 * optional bytes account_address = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAccountAddress()`
 * @return {!Uint8Array}
 */
TronWebProto.AccountCreateContract.prototype.getAccountAddress_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getAccountAddress()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.AccountCreateContract} returns this
 */
TronWebProto.AccountCreateContract.prototype.setAccountAddress = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};

/**
 * optional AccountType type = 3;
 * @return {!TronWebProto.AccountType}
 */
TronWebProto.AccountCreateContract.prototype.getType = function () {
    return /** @type {!TronWebProto.AccountType} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};

/**
 * @param {!TronWebProto.AccountType} value
 * @return {!TronWebProto.AccountCreateContract} returns this
 */
TronWebProto.AccountCreateContract.prototype.setType = function (value) {
    return jspb.Message.setProto3EnumField(this, 3, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    TronWebProto.AccountUpdateContract.prototype.toObject = function (opt_includeInstance) {
        return TronWebProto.AccountUpdateContract.toObject(opt_includeInstance, this);
    };

    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!TronWebProto.AccountUpdateContract} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    TronWebProto.AccountUpdateContract.toObject = function (includeInstance, msg) {
        var f,
            obj = {
                accountName: msg.getAccountName_asB64(),
                ownerAddress: msg.getOwnerAddress_asB64(),
            };

        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!TronWebProto.AccountUpdateContract}
 */
TronWebProto.AccountUpdateContract.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new TronWebProto.AccountUpdateContract();
    return TronWebProto.AccountUpdateContract.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!TronWebProto.AccountUpdateContract} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!TronWebProto.AccountUpdateContract}
 */
TronWebProto.AccountUpdateContract.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setAccountName(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setOwnerAddress(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
TronWebProto.AccountUpdateContract.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    TronWebProto.AccountUpdateContract.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!TronWebProto.AccountUpdateContract} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
TronWebProto.AccountUpdateContract.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getAccountName_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getOwnerAddress_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
};

/**
 * optional bytes account_name = 1;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.AccountUpdateContract.prototype.getAccountName = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * optional bytes account_name = 1;
 * This is a type-conversion wrapper around `getAccountName()`
 * @return {string}
 */
TronWebProto.AccountUpdateContract.prototype.getAccountName_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getAccountName()));
};

/**
 * optional bytes account_name = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAccountName()`
 * @return {!Uint8Array}
 */
TronWebProto.AccountUpdateContract.prototype.getAccountName_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getAccountName()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.AccountUpdateContract} returns this
 */
TronWebProto.AccountUpdateContract.prototype.setAccountName = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};

/**
 * optional bytes owner_address = 2;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.AccountUpdateContract.prototype.getOwnerAddress = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * optional bytes owner_address = 2;
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {string}
 */
TronWebProto.AccountUpdateContract.prototype.getOwnerAddress_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getOwnerAddress()));
};

/**
 * optional bytes owner_address = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {!Uint8Array}
 */
TronWebProto.AccountUpdateContract.prototype.getOwnerAddress_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getOwnerAddress()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.AccountUpdateContract} returns this
 */
TronWebProto.AccountUpdateContract.prototype.setOwnerAddress = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};

if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    TronWebProto.SetAccountIdContract.prototype.toObject = function (opt_includeInstance) {
        return TronWebProto.SetAccountIdContract.toObject(opt_includeInstance, this);
    };

    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!TronWebProto.SetAccountIdContract} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    TronWebProto.SetAccountIdContract.toObject = function (includeInstance, msg) {
        var f,
            obj = {
                accountId: msg.getAccountId_asB64(),
                ownerAddress: msg.getOwnerAddress_asB64(),
            };

        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!TronWebProto.SetAccountIdContract}
 */
TronWebProto.SetAccountIdContract.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new TronWebProto.SetAccountIdContract();
    return TronWebProto.SetAccountIdContract.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!TronWebProto.SetAccountIdContract} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!TronWebProto.SetAccountIdContract}
 */
TronWebProto.SetAccountIdContract.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setAccountId(value);
                break;
            case 2:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setOwnerAddress(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
TronWebProto.SetAccountIdContract.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    TronWebProto.SetAccountIdContract.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!TronWebProto.SetAccountIdContract} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
TronWebProto.SetAccountIdContract.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getAccountId_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getOwnerAddress_asU8();
    if (f.length > 0) {
        writer.writeBytes(2, f);
    }
};

/**
 * optional bytes account_id = 1;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.SetAccountIdContract.prototype.getAccountId = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * optional bytes account_id = 1;
 * This is a type-conversion wrapper around `getAccountId()`
 * @return {string}
 */
TronWebProto.SetAccountIdContract.prototype.getAccountId_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getAccountId()));
};

/**
 * optional bytes account_id = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAccountId()`
 * @return {!Uint8Array}
 */
TronWebProto.SetAccountIdContract.prototype.getAccountId_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getAccountId()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.SetAccountIdContract} returns this
 */
TronWebProto.SetAccountIdContract.prototype.setAccountId = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};

/**
 * optional bytes owner_address = 2;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.SetAccountIdContract.prototype.getOwnerAddress = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ''));
};

/**
 * optional bytes owner_address = 2;
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {string}
 */
TronWebProto.SetAccountIdContract.prototype.getOwnerAddress_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getOwnerAddress()));
};

/**
 * optional bytes owner_address = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {!Uint8Array}
 */
TronWebProto.SetAccountIdContract.prototype.getOwnerAddress_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getOwnerAddress()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.SetAccountIdContract} returns this
 */
TronWebProto.SetAccountIdContract.prototype.setOwnerAddress = function (value) {
    return jspb.Message.setProto3BytesField(this, 2, value);
};

/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
TronWebProto.AccountPermissionUpdateContract.repeatedFields_ = [4];

if (jspb.Message.GENERATE_TO_OBJECT) {
    /**
     * Creates an object representation of this proto.
     * Field names that are reserved in JavaScript and will be renamed to pb_name.
     * Optional fields that are not set will be set to undefined.
     * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
     * For the list of reserved names please see:
     *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
     * @param {boolean=} opt_includeInstance Deprecated. whether to include the
     *     JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @return {!Object}
     */
    TronWebProto.AccountPermissionUpdateContract.prototype.toObject = function (opt_includeInstance) {
        return TronWebProto.AccountPermissionUpdateContract.toObject(opt_includeInstance, this);
    };

    /**
     * Static version of the {@see toObject} method.
     * @param {boolean|undefined} includeInstance Deprecated. Whether to include
     *     the JSPB instance for transitional soy proto support:
     *     http://goto/soy-param-migration
     * @param {!TronWebProto.AccountPermissionUpdateContract} msg The msg instance to transform.
     * @return {!Object}
     * @suppress {unusedLocalVariables} f is only used for nested messages
     */
    TronWebProto.AccountPermissionUpdateContract.toObject = function (includeInstance, msg) {
        var f,
            obj = {
                ownerAddress: msg.getOwnerAddress_asB64(),
                owner: (f = msg.getOwner()) && core_Tron_pb.Permission.toObject(includeInstance, f),
                witness: (f = msg.getWitness()) && core_Tron_pb.Permission.toObject(includeInstance, f),
                activesList: jspb.Message.toObjectList(msg.getActivesList(), core_Tron_pb.Permission.toObject, includeInstance),
            };

        if (includeInstance) {
            obj.$jspbMessageInstance = msg;
        }
        return obj;
    };
}

/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!TronWebProto.AccountPermissionUpdateContract}
 */
TronWebProto.AccountPermissionUpdateContract.deserializeBinary = function (bytes) {
    var reader = new jspb.BinaryReader(bytes);
    var msg = new TronWebProto.AccountPermissionUpdateContract();
    return TronWebProto.AccountPermissionUpdateContract.deserializeBinaryFromReader(msg, reader);
};

/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!TronWebProto.AccountPermissionUpdateContract} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!TronWebProto.AccountPermissionUpdateContract}
 */
TronWebProto.AccountPermissionUpdateContract.deserializeBinaryFromReader = function (msg, reader) {
    while (reader.nextField()) {
        if (reader.isEndGroup()) {
            break;
        }
        var field = reader.getFieldNumber();
        switch (field) {
            case 1:
                var value = /** @type {!Uint8Array} */ (reader.readBytes());
                msg.setOwnerAddress(value);
                break;
            case 2:
                var value = new core_Tron_pb.Permission();
                reader.readMessage(value, core_Tron_pb.Permission.deserializeBinaryFromReader);
                msg.setOwner(value);
                break;
            case 3:
                var value = new core_Tron_pb.Permission();
                reader.readMessage(value, core_Tron_pb.Permission.deserializeBinaryFromReader);
                msg.setWitness(value);
                break;
            case 4:
                var value = new core_Tron_pb.Permission();
                reader.readMessage(value, core_Tron_pb.Permission.deserializeBinaryFromReader);
                msg.addActives(value);
                break;
            default:
                reader.skipField();
                break;
        }
    }
    return msg;
};

/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.serializeBinary = function () {
    var writer = new jspb.BinaryWriter();
    TronWebProto.AccountPermissionUpdateContract.serializeBinaryToWriter(this, writer);
    return writer.getResultBuffer();
};

/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!TronWebProto.AccountPermissionUpdateContract} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
TronWebProto.AccountPermissionUpdateContract.serializeBinaryToWriter = function (message, writer) {
    var f = undefined;
    f = message.getOwnerAddress_asU8();
    if (f.length > 0) {
        writer.writeBytes(1, f);
    }
    f = message.getOwner();
    if (f != null) {
        writer.writeMessage(2, f, core_Tron_pb.Permission.serializeBinaryToWriter);
    }
    f = message.getWitness();
    if (f != null) {
        writer.writeMessage(3, f, core_Tron_pb.Permission.serializeBinaryToWriter);
    }
    f = message.getActivesList();
    if (f.length > 0) {
        writer.writeRepeatedMessage(4, f, core_Tron_pb.Permission.serializeBinaryToWriter);
    }
};

/**
 * optional bytes owner_address = 1;
 * @return {!(string|Uint8Array)}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.getOwnerAddress = function () {
    return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 1, ''));
};

/**
 * optional bytes owner_address = 1;
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {string}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.getOwnerAddress_asB64 = function () {
    return /** @type {string} */ (jspb.Message.bytesAsB64(this.getOwnerAddress()));
};

/**
 * optional bytes owner_address = 1;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getOwnerAddress()`
 * @return {!Uint8Array}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.getOwnerAddress_asU8 = function () {
    return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(this.getOwnerAddress()));
};

/**
 * @param {!(string|Uint8Array)} value
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.setOwnerAddress = function (value) {
    return jspb.Message.setProto3BytesField(this, 1, value);
};

/**
 * optional Permission owner = 2;
 * @return {?TronWebProto.Permission}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.getOwner = function () {
    return /** @type{?TronWebProto.Permission} */ (jspb.Message.getWrapperField(this, core_Tron_pb.Permission, 2));
};

/**
 * @param {?TronWebProto.Permission|undefined} value
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.setOwner = function (value) {
    return jspb.Message.setWrapperField(this, 2, value);
};

/**
 * Clears the message field making it undefined.
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.clearOwner = function () {
    return this.setOwner(undefined);
};

/**
 * Returns whether this field is set.
 * @return {boolean}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.hasOwner = function () {
    return jspb.Message.getField(this, 2) != null;
};

/**
 * optional Permission witness = 3;
 * @return {?TronWebProto.Permission}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.getWitness = function () {
    return /** @type{?TronWebProto.Permission} */ (jspb.Message.getWrapperField(this, core_Tron_pb.Permission, 3));
};

/**
 * @param {?TronWebProto.Permission|undefined} value
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.setWitness = function (value) {
    return jspb.Message.setWrapperField(this, 3, value);
};

/**
 * Clears the message field making it undefined.
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.clearWitness = function () {
    return this.setWitness(undefined);
};

/**
 * Returns whether this field is set.
 * @return {boolean}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.hasWitness = function () {
    return jspb.Message.getField(this, 3) != null;
};

/**
 * repeated Permission actives = 4;
 * @return {!Array<!TronWebProto.Permission>}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.getActivesList = function () {
    return /** @type{!Array<!TronWebProto.Permission>} */ (
        jspb.Message.getRepeatedWrapperField(this, core_Tron_pb.Permission, 4)
    );
};

/**
 * @param {!Array<!TronWebProto.Permission>} value
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.setActivesList = function (value) {
    return jspb.Message.setRepeatedWrapperField(this, 4, value);
};

/**
 * @param {!TronWebProto.Permission=} opt_value
 * @param {number=} opt_index
 * @return {!TronWebProto.Permission}
 */
TronWebProto.AccountPermissionUpdateContract.prototype.addActives = function (opt_value, opt_index) {
    return jspb.Message.addToRepeatedWrapperField(this, 4, opt_value, TronWebProto.Permission, opt_index);
};

/**
 * Clears the list making it empty but non-null.
 * @return {!TronWebProto.AccountPermissionUpdateContract} returns this
 */
TronWebProto.AccountPermissionUpdateContract.prototype.clearActivesList = function () {
    return this.setActivesList([]);
};

goog.object.extend(exports, TronWebProto);
