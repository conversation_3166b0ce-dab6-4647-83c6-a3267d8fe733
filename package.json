{"name": "tron-usdt-nile", "version": "1.0.0", "description": "Read & transfer USDT on Tron Nile via RPC (TronWeb + TS)", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "start:prod": "node dist/index.js", "test": "ts-node src/test.ts"}, "dependencies": {"dotenv": "^16.0.0", "tronweb": "^6.0.3"}, "devDependencies": {"@types/node": "^18.0.0", "ts-node": "^10.0.0", "typescript": "^4.0.0"}}