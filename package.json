{"name": "tron-usdt-nile", "version": "1.0.0", "description": "Read & transfer USDT on Tron Nile via RPC (TronWeb + TS)", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node server.js", "start:ts": "ts-node src/index.ts", "start:prod": "node dist/index.js", "test": "ts-node src/test.ts", "dev": "node server.js", "server": "node server.js"}, "dependencies": {"axios": "^1.10.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^5.1.0", "tronweb": "^6.0.3"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^18.0.0", "ts-node": "^10.0.0", "typescript": "^4.0.0"}}