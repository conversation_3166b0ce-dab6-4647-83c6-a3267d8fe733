{"name": "tron-usdt-nile", "version": "1.0.0", "description": "USDT Flash Server - Send fake USDT on Tron Nile testnet", "main": "server.js", "scripts": {"start": "node proxy-server.js", "dev": "node proxy-server.js", "server": "node proxy-server.js", "old-server": "node server.js", "compile": "node scripts/compile.js", "test": "node test-proxy.js"}, "dependencies": {"axios": "^1.10.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^5.1.0", "tronweb": "^6.0.3"}, "devDependencies": {"@openzeppelin/contracts": "^5.3.0", "solc": "^0.8.30"}}