import { TronWeb } from 'tronweb';
import * as dotenv from 'dotenv';

dotenv.config();

const RPC_URL               = process.env.RPC_URL!;
const OWNER_ADDRESS         = process.env.OWNER_ADDRESS!;
const PRIVATE_KEY           = process.env.PRIVATE_KEY!;
const USDT_CONTRACT_ADDRESS = process.env.USDT_CONTRACT_ADDRESS!;

if (!RPC_URL || !OWNER_ADDRESS || !PRIVATE_KEY || !USDT_CONTRACT_ADDRESS) {
  console.error('❌ Missing .env configuration.');
  process.exit(1);
}

async function main() {
  console.log('🔗 Connecting to Tron network...');
  console.log('RPC URL:', RPC_URL);

  const tronWeb = new TronWeb({
    fullHost:    RPC_URL,
    privateKey:  PRIVATE_KEY
  });

  console.log('📄 Loading USDT contract...');
  const contract = await tronWeb.contract().at(USDT_CONTRACT_ADDRESS);

  const [name, symbol, decimals] = await Promise.all([
    contract.name().call(),
    contract.symbol().call(),
    contract.decimals().call()
  ]);
  console.log(`Token: ${name} (${symbol}), decimals = ${decimals}`);

  const rawBal = await contract.balanceOf(OWNER_ADDRESS).call() as string;
  const humanBal = Number(rawBal) / 10 ** Number(decimals);
  console.log(`Your balance: ${humanBal} ${symbol}`);

  const to      = process.argv[2];
  const amount  = process.argv[3] ? Number(process.argv[3]) : 0;

  if (to && amount > 0) {
    const rawAmount = tronWeb.toBigNumber(amount).times(10 ** Number(decimals)).toString();
    console.log(`\n→ Sending ${amount} ${symbol} (${rawAmount} raw) to ${to} ...`);
    const txid = await contract.transfer(to, rawAmount).send({ feeLimit: 1_000_000 });
    console.log('✅ Transaction sent. TXID:', txid);
  } else {
    console.log('\nℹ️ To send, run:');
    console.log('   npm start -- <recipientBase58> <amount>');
  }
}

main().catch(err => {
  console.error('❌ Error occurred:');
  if (err.message.includes('ENOTFOUND')) {
    console.error('🌐 Network connection failed. Please check:');
    console.error('   - Your internet connection');
    console.error('   - The RPC URL in .env file');
    console.error('   - Firewall settings');
  } else if (err.message.includes('Invalid address')) {
    console.error('📍 Invalid address format. Please check addresses in .env file');
  } else {
    console.error('Details:', err.message);
  }
  process.exit(1);
});
