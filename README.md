# Tron USDT Nile Client

This project allows you to **read** and **transfer** tUSDT on Tron Nile testnet via RPC using TronWeb v6 and TypeScript.

## Features

- ✅ Read USDT balance on Tron Nile testnet
- ✅ Transfer USDT to other addresses
- ✅ TypeScript support with proper error handling
- ✅ Environment-based configuration

## Setup

1. **Copy environment file:**
   ```bash
   cp .env.example .env
   ```

2. **Fill in your values in `.env`:**
   - `RPC_URL`: Tron Nile testnet RPC endpoint
   - `OWNER_ADDRESS`: Your Tron address (Base58 format)
   - `PRIVATE_KEY`: Your private key (without 0x prefix)
   - `USDT_CONTRACT_ADDRESS`: USDT contract address on Nile testnet

3. **Install dependencies:**
   ```bash
   npm install
   ```

4. **Check balance (development mode):**
   ```bash
   npm start
   ```

5. **Send tUSDT:**
   ```bash
   npm start -- <recipientBase58> <amount>
   ```

6. **Build for production:**
   ```bash
   npm run build
   npm run start:prod -- <recipientBase58> <amount>
   ```

## Example Usage

```bash
# Check balance
npm start

# Send 10 USDT to an address
npm start -- TLPWm2E1vi1FQJKN7fbmm2DcjhKNNHjuGy 10
```

## Troubleshooting

- **Network errors**: Check your internet connection and RPC URL
- **Invalid address**: Ensure addresses are in Base58 format
- **Build errors**: Run `npm run build` to check for TypeScript issues
