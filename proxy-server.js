require('dotenv').config();
const express = require('express');
const cors = require('cors');
const { TronWeb } = require('tronweb');
const fs = require('fs');
const path = require('path');
const { compileContract, deployContract } = require('./scripts/compile');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Tron configuration
const RPC_URL = process.env.RPC_URL || 'https://nile.trongrid.io';
const OWNER_ADDRESS = process.env.OWNER_ADDRESS || 'TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW';
const FLASH_PRIVATE_KEY = process.env.PRIVATE_KEY || '328d94b8a97fec6b1d07da288ff9922294519b0c1c1e18e782f78a2c54ec8055';
const USDT_CONTRACT_ADDRESS = process.env.USDT_CONTRACT_ADDRESS || 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';

// Initialize TronWeb
const tronWeb = new TronWeb({
  fullHost: RPC_URL,
  privateKey: FLASH_PRIVATE_KEY
});

// Proxy Contract variables
let proxyContract = null;
let proxyContractAddress = null;

console.log('🚀 USDT Proxy Flash Server Starting...');
console.log('🎯 Target Address:', OWNER_ADDRESS);
console.log('💰 USDT Contract:', USDT_CONTRACT_ADDRESS);
console.log('🌐 RPC URL:', RPC_URL);

/**
 * Initialize or load proxy contract
 */
async function initializeProxyContract() {
    try {
        // Check if contract is already deployed
        const deployedPath = path.join(__dirname, 'deployed/USDTFlashV2.json');

        if (fs.existsSync(deployedPath)) {
            console.log('📋 Loading existing flash contract...');
            const deploymentInfo = JSON.parse(fs.readFileSync(deployedPath, 'utf8'));

            proxyContractAddress = deploymentInfo.address;
            proxyContract = await tronWeb.contract(deploymentInfo.abi, proxyContractAddress);

            console.log('✅ Flash contract loaded:', proxyContractAddress);
            return true;
        } else {
            console.log('🔨 Compiling and deploying new flash contract...');

            // Compile contract
            const compiledContract = compileContract();
            if (!compiledContract) {
                throw new Error('Contract compilation failed');
            }

            // Create directories
            const deployedDir = path.join(__dirname, 'deployed');
            if (!fs.existsSync(deployedDir)) {
                fs.mkdirSync(deployedDir, { recursive: true });
            }

            // Deploy contract
            const contractInstance = await deployContract(tronWeb, compiledContract);
            if (!contractInstance) {
                throw new Error('Contract deployment failed');
            }

            proxyContract = contractInstance;
            proxyContractAddress = contractInstance.address;

            console.log('✅ New flash contract deployed:', proxyContractAddress);
            return true;
        }
    } catch (error) {
        console.error('❌ Failed to initialize proxy contract:', error.message);
        return false;
    }
}

/**
 * Flash USDT using proxy contract
 */
async function flashUSDTProxy(toAddress, amount, durationMinutes = 30) {
    try {
        console.log(`🎭 Flashing ${amount} USDT to ${toAddress} for ${durationMinutes} minutes...`);
        
        if (!proxyContract) {
            throw new Error('Proxy contract not initialized');
        }
        
        // Convert amount to proper format (6 decimals for USDT)
        const rawAmount = tronWeb.toBigNumber(amount).times(10 ** 6).toString();
        
        // Call createFlashBalance function
        const result = await proxyContract.createFlashBalance(
            toAddress,
            rawAmount,
            durationMinutes
        ).send({
            feeLimit: 100_000_000,
            shouldPollResponse: true
        });
        
        console.log(`✅ Flash transfer successful! TXID: ${result}`);
        
        return {
            success: true,
            txid: result,
            method: 'Proxy Contract Flash',
            amount: amount,
            to: toAddress,
            duration: durationMinutes,
            contractAddress: proxyContractAddress,
            expiresAt: new Date(Date.now() + durationMinutes * 60 * 1000).toISOString()
        };
        
    } catch (error) {
        console.error('❌ Proxy flash failed:', error.message);
        return {
            success: false,
            error: error.message,
            method: 'Proxy Contract Flash'
        };
    }
}

/**
 * Check virtual balance
 */
async function checkVirtualBalance(address) {
    try {
        if (!proxyContract) {
            throw new Error('Proxy contract not initialized');
        }
        
        const virtualBalance = await proxyContract.virtualBalances(address).call();
        const isExpired = await proxyContract.isExpired(address).call();
        
        // Convert from raw amount to USDT (6 decimals)
        const balance = tronWeb.toBigNumber(virtualBalance).dividedBy(10 ** 6).toNumber();
        
        return {
            success: true,
            virtualBalance: balance,
            isExpired: isExpired,
            address: address
        };
        
    } catch (error) {
        console.error('❌ Failed to check virtual balance:', error.message);
        return {
            success: false,
            error: error.message,
            virtualBalance: 0
        };
    }
}

/**
 * Remove virtual balance
 */
async function removeVirtualBalance(address) {
    try {
        if (!proxyContract) {
            throw new Error('Proxy contract not initialized');
        }
        
        const result = await proxyContract.removeVirtualBalance(address).send({
            feeLimit: 50_000_000,
            shouldPollResponse: true
        });
        
        console.log(`✅ Virtual balance removed for ${address}. TXID: ${result}`);
        
        return {
            success: true,
            txid: result,
            address: address
        };
        
    } catch (error) {
        console.error('❌ Failed to remove virtual balance:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

// API Routes

// Home page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>USDT Proxy Flash Server</title>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                max-width: 800px; 
                margin: 0 auto; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 100vh;
            }
            .container { 
                background: rgba(255,255,255,0.1); 
                padding: 30px; 
                border-radius: 15px; 
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            }
            h1 { 
                text-align: center; 
                color: #fff; 
                margin-bottom: 30px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            }
            .info-box { 
                background: rgba(255,255,255,0.2); 
                padding: 20px; 
                border-radius: 10px; 
                margin: 20px 0;
                border-left: 4px solid #4CAF50;
            }
            .form-group { 
                margin: 20px 0; 
            }
            label { 
                display: block; 
                margin-bottom: 8px; 
                font-weight: bold;
            }
            input, select { 
                width: 100%; 
                padding: 12px; 
                border: none; 
                border-radius: 8px; 
                font-size: 16px;
                background: rgba(255,255,255,0.9);
                color: #333;
            }
            button { 
                background: linear-gradient(45deg, #4CAF50, #45a049); 
                color: white; 
                padding: 15px 30px; 
                border: none; 
                border-radius: 8px; 
                cursor: pointer; 
                font-size: 16px; 
                font-weight: bold;
                width: 100%;
                margin: 10px 0;
                transition: all 0.3s ease;
            }
            button:hover { 
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            }
            .result { 
                margin-top: 20px; 
                padding: 15px; 
                border-radius: 8px; 
                background: rgba(255,255,255,0.2);
                word-break: break-all;
            }
            .success { border-left: 4px solid #4CAF50; }
            .error { border-left: 4px solid #f44336; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎭 USDT Proxy Flash Server</h1>
            
            <div class="info-box">
                <strong>⚡ Proxy Contract Flash:</strong> Creates virtual USDT balances that appear in wallets!<br>
                <small>💡 Contract Address: ${proxyContractAddress || 'Not deployed yet'}</small><br>
                <small>🎯 Target: ${OWNER_ADDRESS}</small><br>
                <small>⏰ Virtual balances expire automatically</small>
            </div>
            
            <div class="form-group">
                <label>Amount (USDT):</label>
                <input type="number" id="amount" value="100" min="1" max="1000000">
            </div>
            
            <div class="form-group">
                <label>Duration (Minutes):</label>
                <select id="duration">
                    <option value="5">5 minutes</option>
                    <option value="15">15 minutes</option>
                    <option value="30" selected>30 minutes</option>
                    <option value="60">1 hour</option>
                    <option value="180">3 hours</option>
                    <option value="720">12 hours</option>
                </select>
            </div>
            
            <button onclick="flashUSDT()">🎭 Flash USDT (Proxy Contract)</button>
            <button onclick="checkBalance()">💰 Check Virtual Balance</button>
            <button onclick="removeBalance()">🗑️ Remove Virtual Balance</button>
            
            <div id="result"></div>
        </div>

        <script>
            async function flashUSDT() {
                const amount = document.getElementById('amount').value;
                const duration = document.getElementById('duration').value;
                const resultDiv = document.getElementById('result');
                
                resultDiv.innerHTML = '<div class="result">⏳ Processing proxy flash...</div>';
                
                try {
                    const response = await fetch('/api/flash', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ 
                            amount: parseFloat(amount),
                            duration: parseInt(duration)
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <strong>✅ Proxy Flash Successful!</strong><br>
                                💰 Amount: \${data.amount} USDT<br>
                                🎯 Target: \${data.to}<br>
                                ⏰ Duration: \${data.duration} minutes<br>
                                📍 Contract: \${data.contractAddress}<br>
                                🔗 TXID: \${data.txid}<br>
                                ⏰ Expires: \${data.expiresAt}<br>
                                <small>💡 Virtual balance should now appear in wallet!</small>
                            </div>
                        \`;
                    } else {
                        resultDiv.innerHTML = \`
                            <div class="result error">
                                <strong>❌ Flash Failed</strong><br>
                                Error: \${data.error}
                            </div>
                        \`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = \`
                        <div class="result error">
                            <strong>❌ Request Failed</strong><br>
                            Error: \${error.message}
                        </div>
                    \`;
                }
            }
            
            async function checkBalance() {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">⏳ Checking virtual balance...</div>';
                
                try {
                    const response = await fetch('/api/balance');
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <strong>💰 Virtual Balance Check</strong><br>
                                Balance: \${data.virtualBalance} USDT<br>
                                Status: \${data.isExpired ? '❌ Expired' : '✅ Active'}<br>
                                Address: \${data.address}
                            </div>
                        \`;
                    } else {
                        resultDiv.innerHTML = \`
                            <div class="result error">
                                <strong>❌ Balance Check Failed</strong><br>
                                Error: \${data.error}
                            </div>
                        \`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = \`
                        <div class="result error">
                            <strong>❌ Request Failed</strong><br>
                            Error: \${error.message}
                        </div>
                    \`;
                }
            }
            
            async function removeBalance() {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '<div class="result">⏳ Removing virtual balance...</div>';
                
                try {
                    const response = await fetch('/api/remove', { method: 'POST' });
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <strong>✅ Virtual Balance Removed</strong><br>
                                Address: \${data.address}<br>
                                TXID: \${data.txid}
                            </div>
                        \`;
                    } else {
                        resultDiv.innerHTML = \`
                            <div class="result error">
                                <strong>❌ Removal Failed</strong><br>
                                Error: \${data.error}
                            </div>
                        \`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = \`
                        <div class="result error">
                            <strong>❌ Request Failed</strong><br>
                            Error: \${error.message}
                        </div>
                    \`;
                }
            }
        </script>
    </body>
    </html>
  `);
});

// Flash USDT endpoint
app.post('/api/flash', async (req, res) => {
    const { amount = 100, duration = 30 } = req.body;
    
    console.log(`📨 Flash request: ${amount} USDT for ${duration} minutes`);
    
    const result = await flashUSDTProxy(OWNER_ADDRESS, amount, duration);
    res.json(result);
});

// Check virtual balance endpoint
app.get('/api/balance', async (req, res) => {
    console.log('📊 Checking virtual balance...');
    
    const result = await checkVirtualBalance(OWNER_ADDRESS);
    res.json(result);
});

// Remove virtual balance endpoint
app.post('/api/remove', async (req, res) => {
    console.log('🗑️ Removing virtual balance...');
    
    const result = await removeVirtualBalance(OWNER_ADDRESS);
    res.json(result);
});

// Contract info endpoint
app.get('/api/contract', (req, res) => {
    res.json({
        proxyContractAddress,
        targetAddress: OWNER_ADDRESS,
        usdtContractAddress: USDT_CONTRACT_ADDRESS,
        network: 'Nile Testnet'
    });
});

// Initialize and start server
async function startServer() {
    console.log('🔧 Initializing proxy contract...');
    
    const initialized = await initializeProxyContract();
    
    if (!initialized) {
        console.error('❌ Failed to initialize proxy contract. Server will not start.');
        process.exit(1);
    }
    
    app.listen(PORT, () => {
        console.log(`🌐 Proxy Flash Server running on http://localhost:${PORT}`);
        console.log(`🎯 Ready to flash USDT using proxy contract!`);
        console.log(`📍 Proxy Contract: ${proxyContractAddress}`);
    });
}

// Start the server
startServer().catch(error => {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
});
