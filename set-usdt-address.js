require('dotenv').config();
const { TronWeb } = require('tronweb');
const fs = require('fs');
const path = require('path');

// Configuration
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const USDT_CONTRACT_ADDRESS = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';

// Initialize TronWeb
const tronWeb = new TronWeb({
    fullHost: 'https://nile.trongrid.io',
    privateKey: PRIVATE_KEY
});

async function setUSDTAddress() {
    try {
        console.log('🔧 Setting USDT contract address...');
        
        // Load deployed contract
        const deployedPath = path.join(__dirname, 'deployed/USDTFlashV2.json');
        if (!fs.existsSync(deployedPath)) {
            console.error('❌ No deployed contract found. Please deploy first.');
            return;
        }
        
        const deploymentInfo = JSON.parse(fs.readFileSync(deployedPath, 'utf8'));
        const contract = await tronWeb.contract(deploymentInfo.abi, deploymentInfo.address);
        
        console.log('📍 Contract Address:', deploymentInfo.address);
        console.log('💰 Setting USDT Address:', USDT_CONTRACT_ADDRESS);
        
        // Convert USDT address to hex
        const usdtHexAddress = tronWeb.address.toHex(USDT_CONTRACT_ADDRESS);
        console.log('🔢 USDT Hex Address:', usdtHexAddress);
        
        // Set the real USDT contract address
        const result = await contract.setRealUSDTContract(usdtHexAddress).send({
            feeLimit: 50_000_000,
            shouldPollResponse: true
        });
        
        console.log('✅ USDT address set successfully!');
        console.log('📋 Transaction:', result);
        
        // Verify the setting
        const currentUSDTAddress = await contract.REAL_USDT_CONTRACT().call();
        const currentUSDTTron = tronWeb.address.fromHex(currentUSDTAddress);
        
        console.log('🔍 Verification:');
        console.log('   Current USDT Address (Hex):', currentUSDTAddress);
        console.log('   Current USDT Address (Tron):', currentUSDTTron);
        console.log('   Expected:', USDT_CONTRACT_ADDRESS);
        console.log('   Match:', currentUSDTTron === USDT_CONTRACT_ADDRESS ? '✅' : '❌');
        
    } catch (error) {
        console.error('❌ Error setting USDT address:', error.message);
    }
}

setUSDTAddress();
