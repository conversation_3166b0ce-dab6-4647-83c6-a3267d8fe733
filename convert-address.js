const { TronWeb } = require('tronweb');

// Initialize TronWeb
const tronWeb = new TronWeb({
  fullHost: 'https://nile.trongrid.io'
});

// Convert Tron address to hex
function convertTronAddressToHex(tronAddress) {
    try {
        const hexAddress = tronWeb.address.toHex(tronAddress);
        console.log(`🔄 Converting Tron address to Hex:`);
        console.log(`📍 Tron Address: ${tronAddress}`);
        console.log(`🔢 Hex Address: ${hexAddress}`);
        console.log(`✅ Solidity Format: address(${hexAddress})`);
        return hexAddress;
    } catch (error) {
        console.error('❌ Conversion failed:', error.message);
        return null;
    }
}

// Convert the USDT contract address
const usdtTronAddress = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
const usdtHexAddress = convertTronAddressToHex(usdtTronAddress);

console.log('\n🎯 Use this in Solidity contract:');
console.log(`REAL_USDT_CONTRACT = ${usdtHexAddress};`);
