<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Advanced USDT Flash Server - Tron Nile</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .status-section, .flash-section, .results-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .status-label {
            font-weight: 600;
            color: #555;
        }

        .status-value {
            font-weight: 500;
            color: #333;
        }

        .contract-selector {
            padding: 8px 12px;
            border: 2px solid #667eea;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            min-width: 200px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            border-left: 4px solid #28a745;
        }

        .result-success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .result-error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .result-warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .transaction-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }

        .transaction-link:hover {
            text-decoration: underline;
        }

        .method-tabs {
            display: flex;
            margin-bottom: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
        }

        .method-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .method-tab.active {
            background: #667eea;
            color: white;
        }

        .method-content {
            display: none;
        }

        .method-content.active {
            display: block;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Advanced USDT Flash Server - Tron Nile Testnet</h1>
        
        <div class="status-section">
            <h2>📊 Server Status</h2>
            <div class="status-grid">
                <div class="status-item">
                    <span class="status-label">Network:</span>
                    <span class="status-value" id="network">Tron Nile Testnet</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Contract Type:</span>
                    <select id="contractType" class="contract-selector">
                        <option value="mimic">USDTMimicFlash (Advanced)</option>
                        <option value="impersonator">USDTImpersonator (Stealth)</option>
                        <option value="proxy">USDTFlashV2 (Proxy)</option>
                    </select>
                </div>
                <div class="status-item">
                    <span class="status-label">Contract:</span>
                    <span class="status-value" id="contract">Loading...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Status:</span>
                    <span class="status-value" id="status">🟢 Online</span>
                </div>
            </div>
        </div>

        <div class="flash-section">
            <h2>⚡ Flash USDT Transfer</h2>
            
            <div class="method-tabs">
                <div class="method-tab active" onclick="switchMethod('basic')">Basic Flash</div>
                <div class="method-tab" onclick="switchMethod('advanced')">Advanced Flash</div>
                <div class="method-tab" onclick="switchMethod('stealth')">Stealth Mode</div>
            </div>

            <!-- Basic Flash Method -->
            <div id="basic-method" class="method-content active">
                <div class="info-box">
                    <h4>🎯 Basic Flash Transfer</h4>
                    <p>Creates a standard flash USDT transfer using proxy contract events.</p>
                </div>
                
                <div class="form-group">
                    <label for="basicWallet">Target Wallet Address:</label>
                    <input type="text" id="basicWallet" placeholder="TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW" value="TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW">
                </div>
                
                <div class="form-group">
                    <label for="basicAmount">Amount (USDT):</label>
                    <input type="number" id="basicAmount" placeholder="1000" value="1000" min="1" max="1000000">
                </div>
                
                <div class="form-group">
                    <label for="basicDuration">Duration (Minutes):</label>
                    <input type="number" id="basicDuration" placeholder="60" value="60" min="1" max="1440">
                </div>
                
                <button class="btn" onclick="executeBasicFlash()">
                    <span id="basicBtnText">🚀 Execute Basic Flash</span>
                </button>
            </div>

            <!-- Advanced Flash Method -->
            <div id="advanced-method" class="method-content">
                <div class="info-box">
                    <h4>🔥 Advanced Flash Transfer</h4>
                    <p>Uses multiple techniques to make transfers appear more legitimate in blockchain explorers.</p>
                </div>
                
                <div class="form-group">
                    <label for="advancedWallet">Target Wallet Address:</label>
                    <input type="text" id="advancedWallet" placeholder="TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW" value="TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW">
                </div>
                
                <div class="form-group">
                    <label for="advancedAmount">Amount (USDT):</label>
                    <input type="number" id="advancedAmount" placeholder="1000" value="1000" min="1" max="1000000">
                </div>
                
                <div class="form-group">
                    <label for="advancedDuration">Duration (Minutes):</label>
                    <input type="number" id="advancedDuration" placeholder="60" value="60" min="1" max="1440">
                </div>
                
                <button class="btn" onclick="executeAdvancedFlash()">
                    <span id="advancedBtnText">🔥 Execute Advanced Flash</span>
                </button>
            </div>

            <!-- Stealth Flash Method -->
            <div id="stealth-method" class="method-content">
                <div class="info-box">
                    <h4>👻 Stealth Flash Transfer</h4>
                    <p>Attempts to impersonate real USDT contract behavior for maximum authenticity.</p>
                </div>
                
                <div class="form-group">
                    <label for="stealthWallet">Target Wallet Address:</label>
                    <input type="text" id="stealthWallet" placeholder="TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW" value="TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW">
                </div>
                
                <div class="form-group">
                    <label for="stealthAmount">Amount (USDT):</label>
                    <input type="number" id="stealthAmount" placeholder="1000" value="1000" min="1" max="1000000">
                </div>
                
                <div class="form-group">
                    <label for="stealthDuration">Duration (Minutes):</label>
                    <input type="number" id="stealthDuration" placeholder="60" value="60" min="1" max="1440">
                </div>
                
                <button class="btn" onclick="executeStealthFlash()">
                    <span id="stealthBtnText">👻 Execute Stealth Flash</span>
                </button>
            </div>
        </div>

        <div class="results-section">
            <h2>📋 Transaction Results</h2>
            <div id="results">
                <p>No transactions yet. Execute a flash transfer to see results.</p>
            </div>
        </div>
    </div>

    <script>
        let currentMethod = 'basic';
        
        function switchMethod(method) {
            // Update tabs
            document.querySelectorAll('.method-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update content
            document.querySelectorAll('.method-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(method + '-method').classList.add('active');
            
            currentMethod = method;
        }
        
        async function executeBasicFlash() {
            const wallet = document.getElementById('basicWallet').value;
            const amount = document.getElementById('basicAmount').value;
            const duration = document.getElementById('basicDuration').value;
            
            await executeFlash('basic', wallet, amount, duration, 'basicBtnText');
        }
        
        async function executeAdvancedFlash() {
            const wallet = document.getElementById('advancedWallet').value;
            const amount = document.getElementById('advancedAmount').value;
            const duration = document.getElementById('advancedDuration').value;
            
            await executeFlash('advanced', wallet, amount, duration, 'advancedBtnText');
        }
        
        async function executeStealthFlash() {
            const wallet = document.getElementById('stealthWallet').value;
            const amount = document.getElementById('stealthAmount').value;
            const duration = document.getElementById('stealthDuration').value;
            
            await executeFlash('stealth', wallet, amount, duration, 'stealthBtnText');
        }
        
        async function executeFlash(method, wallet, amount, duration, btnId) {
            const btnElement = document.getElementById(btnId);
            const originalText = btnElement.innerHTML;
            
            try {
                // Validate inputs
                if (!wallet || !amount || !duration) {
                    throw new Error('Please fill in all fields');
                }
                
                if (!wallet.startsWith('T') || wallet.length !== 34) {
                    throw new Error('Invalid Tron wallet address');
                }
                
                // Update button
                btnElement.innerHTML = '<span class="loading"></span> Processing...';
                
                // Make API call
                const response = await fetch('/api/flash', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        method: method,
                        wallet: wallet,
                        amount: parseFloat(amount),
                        duration: parseInt(duration)
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResult(result, 'success');
                } else {
                    displayResult(result, 'error');
                }
                
            } catch (error) {
                displayResult({
                    success: false,
                    error: error.message
                }, 'error');
            } finally {
                btnElement.innerHTML = originalText;
            }
        }
        
        function displayResult(result, type) {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleString();
            
            let resultClass = 'result-' + type;
            let icon = type === 'success' ? '✅' : '❌';
            
            let html = `
                <div class="result-box ${resultClass}">
                    <h4>${icon} ${type === 'success' ? 'Transaction Successful' : 'Transaction Failed'}</h4>
                    <p><strong>Time:</strong> ${timestamp}</p>
            `;
            
            if (result.success) {
                html += `
                    <p><strong>Transaction Hash:</strong> <a href="https://nile.tronscan.org/#/transaction/${result.txHash}" target="_blank" class="transaction-link">${result.txHash}</a></p>
                    <p><strong>Target Wallet:</strong> ${result.wallet}</p>
                    <p><strong>Amount:</strong> ${result.amount} USDT</p>
                    <p><strong>Duration:</strong> ${result.duration} minutes</p>
                    <p><strong>Method:</strong> ${result.method}</p>
                    <p><strong>Status:</strong> ${result.status}</p>
                `;
                
                if (result.events && result.events.length > 0) {
                    html += `<p><strong>Events:</strong> ${result.events.length} events emitted</p>`;
                }
            } else {
                html += `<p><strong>Error:</strong> ${result.error}</p>`;
            }
            
            html += '</div>';
            
            resultsDiv.innerHTML = html + resultsDiv.innerHTML;
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Load contract info
            loadContractInfo();
            
            // Update contract type when changed
            document.getElementById('contractType').addEventListener('change', function() {
                loadContractInfo();
            });
        });
        
        async function loadContractInfo() {
            try {
                const contractType = document.getElementById('contractType').value;
                const response = await fetch(`/api/contract-info?type=${contractType}`);
                const info = await response.json();
                
                document.getElementById('contract').textContent = info.address || 'Not deployed';
                document.getElementById('status').textContent = info.status || '🔴 Offline';
                
            } catch (error) {
                console.error('Failed to load contract info:', error);
                document.getElementById('contract').textContent = 'Error loading';
                document.getElementById('status').textContent = '🔴 Error';
            }
        }
    </script>
</body>
</html>
