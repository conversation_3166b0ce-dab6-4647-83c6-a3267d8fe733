function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
import toDate from './toDate';
export default function isAfter(date, options) {
  // For backwards compatibility:
  // isAfter(str [, date]), i.e. `options` could be used as argument for the legacy `date`
  var comparisonDate = (_typeof(options) === 'object' ? options.comparisonDate : options) || Date().toString();
  var comparison = toDate(comparisonDate);
  var original = toDate(date);
  return !!(original && comparison && original > comparison);
}