const express = require('express');
const cors = require('cors');
const { TronWeb } = require('tronweb');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Environment variables
const RPC_URL = process.env.RPC_URL;
const FLASH_PRIVATE_KEY = process.env.PRIVATE_KEY; // مفتاح محفظة الفلاش
const TARGET_ADDRESS = process.env.OWNER_ADDRESS; // العنوان المستهدف للإرسال إليه
const USDT_CONTRACT_ADDRESS = process.env.USDT_CONTRACT_ADDRESS;

if (!RPC_URL || !FLASH_PRIVATE_KEY || !TARGET_ADDRESS || !USDT_CONTRACT_ADDRESS) {
  console.error('❌ Missing .env configuration.');
  process.exit(1);
}

// Initialize TronWeb
const tronWeb = new TronWeb({
  fullHost: RPC_URL,
  privateKey: FLASH_PRIVATE_KEY
});

console.log('🚀 USDT Flash Server Starting...');
console.log('🎯 Target Address:', TARGET_ADDRESS);
console.log('💰 USDT Contract:', USDT_CONTRACT_ADDRESS);
console.log('🌐 RPC URL:', RPC_URL);

// Flash USDT function - Creates fake transactions
async function flashUSDT(toAddress, amount) {
  try {
    console.log(`💸 Flashing ${amount} USDT to ${toAddress}...`);

    // Generate fake transaction ID
    const fakeTransactionId = generateFakeTxId();

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Store fake balance in memory (in real implementation, you'd use a database)
    if (!global.fakeBalances) {
      global.fakeBalances = {};
    }

    if (!global.fakeBalances[toAddress]) {
      global.fakeBalances[toAddress] = 0;
    }

    global.fakeBalances[toAddress] += amount;

    console.log(`✅ Flash USDT sent! Fake TXID: ${fakeTransactionId}`);
    console.log(`💰 New fake balance for ${toAddress}: ${global.fakeBalances[toAddress]} USDT`);

    return {
      success: true,
      txid: fakeTransactionId,
      message: `Successfully flashed ${amount} USDT to ${toAddress}`,
      newBalance: global.fakeBalances[toAddress]
    };

  } catch (error) {
    console.error('❌ Flash failed:', error.message);
    return { success: false, error: error.message };
  }
}

// Generate fake transaction ID
function generateFakeTxId() {
  const chars = '0123456789abcdef';
  let result = '';
  for (let i = 0; i < 64; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Check balance function - Shows fake balance + real balance
async function checkBalance(address) {
  try {
    // Get real balance from blockchain
    let realBalance = 0;
    try {
      const contract = await tronWeb.contract().at(USDT_CONTRACT_ADDRESS);
      const [rawBalance, decimals] = await Promise.all([
        contract.balanceOf(address).call(),
        contract.decimals().call()
      ]);
      realBalance = Number(rawBalance) / 10 ** Number(decimals);
    } catch (blockchainError) {
      console.log('⚠️ Could not fetch real balance:', blockchainError.message);
    }

    // Get fake balance from memory
    const fakeBalance = global.fakeBalances && global.fakeBalances[address] ? global.fakeBalances[address] : 0;

    // Total balance = real + fake
    const totalBalance = realBalance + fakeBalance;

    console.log(`💰 Balance check for ${address}:`);
    console.log(`   Real balance: ${realBalance} USDT`);
    console.log(`   Fake balance: ${fakeBalance} USDT`);
    console.log(`   Total balance: ${totalBalance} USDT`);

    return {
      success: true,
      balance: totalBalance,
      realBalance: realBalance,
      fakeBalance: fakeBalance
    };

  } catch (error) {
    return { success: false, error: error.message };
  }
}

// API Routes

// Home page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>USDT Flash Server</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .container { background: #f5f5f5; padding: 30px; border-radius: 10px; }
            .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            .button:hover { background: #0056b3; }
            .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
            input { padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 5px; width: 300px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>💸 USDT Flash Server</h1>
            <div style="background: #fff3cd; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>⚡ Flash Mode:</strong> This server creates fake USDT transactions that appear real temporarily!
            </div>
            <p><strong>Target Address:</strong> ${TARGET_ADDRESS}</p>
            <p><strong>USDT Contract:</strong> ${USDT_CONTRACT_ADDRESS}</p>
            
            <h3>🚀 Flash USDT</h3>
            <div>
                <input type="number" id="amount" placeholder="Amount (USDT)" value="100">
                <button class="button" onclick="flashToTarget()">Flash to Target Address</button>
            </div>
            
            <h3>💰 Check Balance</h3>
            <div>
                <input type="text" id="checkAddress" placeholder="Address to check" value="${TARGET_ADDRESS}">
                <button class="button" onclick="checkBalance()">Check Balance</button>
            </div>
            
            <div id="result"></div>
        </div>

        <script>
            async function flashToTarget() {
                const amount = document.getElementById('amount').value;
                const result = document.getElementById('result');
                
                result.innerHTML = '<div class="result">⏳ Flashing USDT...</div>';
                
                try {
                    const response = await fetch('/api/flash', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ amount: parseFloat(amount) })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        result.innerHTML = \`<div class="result success">
                            ✅ Flash successful!<br>
                            💸 Amount: \${amount} USDT<br>
                            🎯 To: ${TARGET_ADDRESS}<br>
                            📄 Fake TXID: \${data.txid}<br>
                            💰 New Balance: \${data.newBalance} USDT
                        </div>\`;
                    } else {
                        result.innerHTML = \`<div class="result error">❌ Flash failed: \${data.error}</div>\`;
                    }
                } catch (error) {
                    result.innerHTML = \`<div class="result error">❌ Error: \${error.message}</div>\`;
                }
            }
            
            async function checkBalance() {
                const address = document.getElementById('checkAddress').value;
                const result = document.getElementById('result');
                
                result.innerHTML = '<div class="result">⏳ Checking balance...</div>';
                
                try {
                    const response = await fetch(\`/api/balance/\${address}\`);
                    const data = await response.json();
                    
                    if (data.success) {
                        result.innerHTML = \`<div class="result success">
                            💰 Total Balance: \${data.balance} USDT<br>
                            📊 Real Balance: \${data.realBalance} USDT<br>
                            ✨ Fake Balance: \${data.fakeBalance} USDT
                        </div>\`;
                    } else {
                        result.innerHTML = \`<div class="result error">❌ Error: \${data.error}</div>\`;
                    }
                } catch (error) {
                    result.innerHTML = \`<div class="result error">❌ Error: \${error.message}</div>\`;
                }
            }
        </script>
    </body>
    </html>
  `);
});

// Flash USDT to target address
app.post('/api/flash', async (req, res) => {
  try {
    const { amount } = req.body;
    
    if (!amount || amount <= 0) {
      return res.json({ success: false, error: 'Invalid amount' });
    }
    
    console.log(`📨 Flash request: ${amount} USDT to ${TARGET_ADDRESS}`);
    const result = await flashUSDT(TARGET_ADDRESS, amount);
    res.json(result);
    
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Check balance
app.get('/api/balance/:address', async (req, res) => {
  try {
    const { address } = req.params;
    
    if (!address) {
      return res.json({ success: false, error: 'Address required' });
    }
    
    const result = await checkBalance(address);
    res.json(result);
    
  } catch (error) {
    res.json({ success: false, error: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🌐 Server running on http://localhost:${PORT}`);
  console.log(`🎯 Ready to flash USDT to: ${TARGET_ADDRESS}`);
});
