"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
const tronweb_1 = require("tronweb");
const dotenv = __importStar(require("dotenv"));
dotenv.config();
const RPC_URL = process.env.RPC_URL;
const OWNER_ADDRESS = process.env.OWNER_ADDRESS;
const PRIVATE_KEY = process.env.PRIVATE_KEY;
const USDT_CONTRACT_ADDRESS = process.env.USDT_CONTRACT_ADDRESS;
if (!RPC_URL || !OWNER_ADDRESS || !PRIVATE_KEY || !USDT_CONTRACT_ADDRESS) {
    console.error('❌ Missing .env configuration.');
    process.exit(1);
}
async function main() {
    const tronWeb = new tronweb_1.TronWeb({
        fullHost: RPC_URL,
        privateKey: PRIVATE_KEY
    });
    const contract = await tronWeb.contract().at(USDT_CONTRACT_ADDRESS);
    const [name, symbol, decimals] = await Promise.all([
        contract.name().call(),
        contract.symbol().call(),
        contract.decimals().call()
    ]);
    console.log(`Token: ${name} (${symbol}), decimals = ${decimals}`);
    const rawBal = await contract.balanceOf(OWNER_ADDRESS).call();
    const humanBal = Number(rawBal) / 10 ** Number(decimals);
    console.log(`Your balance: ${humanBal} ${symbol}`);
    const to = process.argv[2];
    const amount = process.argv[3] ? Number(process.argv[3]) : 0;
    if (to && amount > 0) {
        const rawAmount = tronWeb.toBigNumber(amount).times(10 ** Number(decimals)).toString();
        console.log(`\n→ Sending ${amount} ${symbol} (${rawAmount} raw) to ${to} ...`);
        const txid = await contract.transfer(to, rawAmount).send({ feeLimit: 1000000 });
        console.log('✅ Transaction sent. TXID:', txid);
    }
    else {
        console.log('\nℹ️ To send, run:');
        console.log('   npm start -- <recipientBase58> <amount>');
    }
}
main().catch(err => {
    console.error('Error:', err);
    process.exit(1);
});
