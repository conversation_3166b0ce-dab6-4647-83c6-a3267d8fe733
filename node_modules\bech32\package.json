{"name": "bech32", "version": "1.1.4", "description": "Bech32 encoding / decoding", "keywords": ["base32", "bech32", "bitcoin", "crypto", "crytography", "decode", "decoding", "encode", "encoding"], "main": "index.js", "types": "index.d.ts", "files": ["index.js", "index.d.ts"], "license": "MIT", "dependencies": {}, "devDependencies": {"nyc": "^15.0.0", "standard": "^14.3.3", "tap-dot": "*", "tape": "^4.13.2"}, "repository": {"url": "http://github.com/bitcoinjs/bech32", "type": "git"}, "scripts": {"coverage": "nyc --check-coverage --branches 90 --functions 90 tape test/*.js", "standard": "standard", "test": "tape test/*.js | tap-dot"}}