import { TronWeb } from 'tronweb';

// Simple test to verify TronWeb import and basic functionality
async function testTronWebImport() {
  console.log('🧪 Testing TronWeb import...');

  try {
    console.log('✅ TronWeb imported successfully');

    // Test static methods that don't require instance
    const testAddress = 'TLPWm2E1vi1FQJKN7fbmm2DcjhKNNHjuGy';
    const isValid = TronWeb.isAddress(testAddress);
    console.log(`📍 Address validation test: ${isValid ? '✅ PASS' : '❌ FAIL'}`);

    // Test hex conversion
    const hexValue = TronWeb.toHex('Hello World');
    console.log(`🔢 Hex conversion test: ${hexValue ? '✅ PASS' : '❌ FAIL'}`);

    // Test BigNumber
    const bigNum = TronWeb.toBigNumber(100);
    console.log(`🔢 BigNumber test: ${bigNum ? '✅ PASS' : '❌ FAIL'}`);

    console.log('🎉 All basic tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testTronWebImport();
