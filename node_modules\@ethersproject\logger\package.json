{"author": "<PERSON> <<EMAIL>>", "dependencies": {}, "description": "Logger utility functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/logger", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/logger", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0x5031d43ca82bad1539d4b8ca4a77f3741709eaf759b48bd5ea001b8a76d0a65e", "types": "./lib/index.d.ts", "version": "5.8.0"}