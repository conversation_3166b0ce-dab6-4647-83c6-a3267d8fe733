{"_ethers.alias": {"sha2.js": "browser-sha2.js"}, "author": "<PERSON> <<EMAIL>>", "browser": {"./lib/sha2": "./lib/browser-sha2.js"}, "dependencies": {"@ethersproject/bytes": "^5.8.0", "@ethersproject/logger": "^5.8.0", "hash.js": "1.1.7"}, "description": "The SHA2 family hash functions and HMAC functions for ethers.", "ethereum": "donations.ethers.eth", "funding": [{"type": "individual", "url": "https://gitcoin.co/grants/13/ethersjs-complete-simple-and-tiny-2"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "gitHead": "fa5f647bb2cde63dd0b9664c42cbfbdc1515e800", "keywords": ["Ethereum", "ethers"], "license": "MIT", "main": "./lib/index.js", "module": "./lib.esm/index.js", "name": "@ethersproject/sha2", "publishConfig": {"access": "public"}, "repository": {"directory": "packages/sha2", "type": "git", "url": "git://github.com/ethers-io/ethers.js.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "sideEffects": false, "tarballHash": "0xa34b4d1cc273b0a2b8e1f2e4e2dfdca49d4069b9ac9695fb6eb9488051193c37", "types": "./lib/index.d.ts", "version": "5.8.0"}