const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function testProxyFlash() {
    console.log('🧪 Testing Proxy Contract Flash...');
    
    try {
        // Test 1: Flash USDT
        console.log('\n📨 Test 1: Sending proxy flash request...');
        const flashResponse = await axios.post(`${BASE_URL}/api/flash`, {
            amount: 100,
            duration: 30
        });
        
        console.log('✅ Flash Response:', JSON.stringify(flashResponse.data, null, 2));
        
        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Test 2: Check virtual balance
        console.log('\n💰 Test 2: Checking virtual balance...');
        const balanceResponse = await axios.get(`${BASE_URL}/api/balance`);
        
        console.log('✅ Balance Response:', JSON.stringify(balanceResponse.data, null, 2));
        
        // Test 3: Get contract info
        console.log('\n📍 Test 3: Getting contract info...');
        const contractResponse = await axios.get(`${BASE_URL}/api/contract`);
        
        console.log('✅ Contract Info:', JSON.stringify(contractResponse.data, null, 2));
        
        // Test 4: Remove virtual balance (optional)
        console.log('\n🗑️ Test 4: Removing virtual balance...');
        const removeResponse = await axios.post(`${BASE_URL}/api/remove`);
        
        console.log('✅ Remove Response:', JSON.stringify(removeResponse.data, null, 2));
        
        console.log('\n🎉 All tests completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

// Run tests
testProxyFlash();
