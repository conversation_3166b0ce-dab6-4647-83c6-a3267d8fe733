const fs = require('fs');
const path = require('path');
const solc = require('solc');

/**
 * Compile Solidity contracts
 */
function compileContract() {
    console.log('🔨 Compiling USDTProxy contract...');
    
    // Read the contract source code
    const contractPath = path.join(__dirname, '../contracts/USDTProxy.sol');
    const source = fs.readFileSync(contractPath, 'utf8');
    
    // Solidity compiler input
    const input = {
        language: 'Solidity',
        sources: {
            'USDTProxy.sol': {
                content: source
            }
        },
        settings: {
            outputSelection: {
                '*': {
                    '*': ['*']
                }
            }
        }
    };
    
    try {
        // Compile the contract
        const output = JSON.parse(solc.compile(JSON.stringify(input)));
        
        // Check for compilation errors
        if (output.errors) {
            output.errors.forEach(error => {
                if (error.severity === 'error') {
                    console.error('❌ Compilation Error:', error.formattedMessage);
                    return null;
                } else {
                    console.warn('⚠️ Warning:', error.formattedMessage);
                }
            });
        }
        
        // Get compiled contract
        const contract = output.contracts['USDTProxy.sol']['USDTProxy'];
        
        if (!contract) {
            console.error('❌ Contract compilation failed');
            return null;
        }
        
        console.log('✅ Contract compiled successfully!');
        
        // Save compiled contract
        const compiledDir = path.join(__dirname, '../compiled');
        if (!fs.existsSync(compiledDir)) {
            fs.mkdirSync(compiledDir, { recursive: true });
        }
        
        const compiledContract = {
            abi: contract.abi,
            bytecode: contract.evm.bytecode.object,
            metadata: contract.metadata
        };
        
        fs.writeFileSync(
            path.join(compiledDir, 'USDTProxy.json'),
            JSON.stringify(compiledContract, null, 2)
        );
        
        console.log('💾 Compiled contract saved to compiled/USDTProxy.json');
        
        return compiledContract;
        
    } catch (error) {
        console.error('❌ Compilation failed:', error.message);
        return null;
    }
}

/**
 * Deploy contract to Tron network
 */
async function deployContract(tronWeb, compiledContract) {
    console.log('🚀 Deploying USDTProxy contract...');
    
    try {
        // Deploy the contract
        const contractInstance = await tronWeb.contract().new({
            abi: compiledContract.abi,
            bytecode: compiledContract.bytecode,
            feeLimit: 1000000000, // 1000 TRX
            callValue: 0,
            userFeePercentage: 100,
            originEnergyLimit: 10000000
        });
        
        console.log('✅ Contract deployed successfully!');
        console.log('📍 Contract Address:', contractInstance.address);
        
        // Save deployment info
        const deploymentInfo = {
            address: contractInstance.address,
            abi: compiledContract.abi,
            deployedAt: new Date().toISOString(),
            network: 'Nile Testnet'
        };
        
        fs.writeFileSync(
            path.join(__dirname, '../deployed/USDTProxy.json'),
            JSON.stringify(deploymentInfo, null, 2)
        );
        
        console.log('💾 Deployment info saved to deployed/USDTProxy.json');
        
        return contractInstance;
        
    } catch (error) {
        console.error('❌ Deployment failed:', error.message);
        return null;
    }
}

module.exports = {
    compileContract,
    deployContract
};

// If run directly
if (require.main === module) {
    compileContract();
}
